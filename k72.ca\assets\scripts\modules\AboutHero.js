import { module } from 'modujs';

export default class extends module {
    constructor(m) {
        super(m);

        this.images = Array.from(this.$('image'));
        this.gap = 1 / this.images.length;

        this.zIndex = 2;
        this.currentStep = 0;
    }

    init() {
        this.hasSmoothScroll = document.documentElement.classList.contains('has-scroll-smooth');

        if(!this.hasSmoothScroll) {
            this.interval = setInterval(() => {
                this.showStep((this.currentStep + 1) % this.images.length)
            }, 1000)
        }
    }

    update(progress) {
        if(!this.hasSmoothScroll) return;

        const step = Math.floor(progress / this.gap)
        if(step != this.currentStep) this.showStep(step);
    }

    showStep(index) {
        this.currentStep = index;
        this.images[index].style.zIndex = this.zIndex;
        this.zIndex++;
    }

    destroy() {
        super.destroy()
        clearInterval(this.interval);
    }
}
