import { module } from 'modujs';
import { slideUp, slideDown } from '../utils/slide';

const ITEM_ACTIVE_CLASS = 'is-active';

export default class extends module {
    constructor(m) {
        super(m);

        this.events = {
            click: {
                'button': 'onButtonClick'
            }
        }
    }

    init() {
        this.buttons = this.$('button')
        this.buttonsKeyDownBind = (e) => {
            if(e.code == 'Space' || e.code == 'Enter') {
                e.curTarget = e.currentTarget
                this.onButtonClick(e)
            }
        }
        for(let header of Array.from(this.buttons)) {
            header.addEventListener('keydown', this.buttonsKeyDownBind)
        }
    }

    onButtonClick(e) {
        // Get matching item
        let item = this.parent('item', e.curTarget)

        // Get parent list
        let list = item.parentNode;
        // Close every other item in this list
        for(let child of Array.from(list.children)) {
            if(child != item) {
                this.closeItem(child)
            }
        }

        // Toggle current item
        this.toggleItem(item);
    }

    toggleItem(item) {
        if(item.classList.contains(ITEM_ACTIVE_CLASS)) {
            this.closeItem(item)
        } else {
            this.openItem(item)
        }
    }

    openItem(item) {
        // Get the content el and open it w/ animation
        let content = this.$('content', item)[0]
        if(content) {
            slideDown(content, 500)
            setTimeout(() => {
                this.call('update', null, 'Scroll')
            }, 500);
        }
        // Add active clas on item for style & status
        item.classList.toggle(ITEM_ACTIVE_CLASS);
    }

    closeItem(item) {
        // Trigger close action only if currently active
        if(item.classList.contains(ITEM_ACTIVE_CLASS)) {
            // Get the content el and close it w/ animation
            let content = this.$('content', item)[0];
            if(content) {
                slideUp(content)
                setTimeout(() => {
                    this.call('update', null, 'Scroll')
                }, 500);
            }
            // Remove active clas on item for style & status
            item.classList.remove(ITEM_ACTIVE_CLASS);
        }
    }

    destroy() {
        for(let button of Array.from(this.buttons)) {
            button.removeEventListener('keydown', this.buttonsKeyDownBind)
        }
    }
}
