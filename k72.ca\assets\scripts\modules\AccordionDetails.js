// Props to https://css-tricks.com/how-to-animate-the-details-element-using-waapi/

import { module as Module } from "modujs";
import { CUSTOM_EVENT } from "../config";

export default class AccordionDetails extends Module {
  static CLASS_ACTIVE = "is-active";
  static EASING = "cubic-bezier(0.33, 1, 0.68, 1)";
  static DURATION = 300;

  constructor(m) {
    super(m);
    // Binding
    this.onClick = this.onClick.bind(this);
    this.onModalClose = this.onModalClose.bind(this);

    // UI
    this.$summary = this.$("summary")[0];
    this.$content = this.$("content")[0];
    this.$parent = this.el.closest("[data-accordion-parent]") || null;
    this.$group = this.el.closest("[data-accordion-group]") || null;
    this.isSingle = this.el.hasAttribute("data-accordion-single");

    // Data
    this.animation = null;
    this.isClosing = false;
    this.isExpanding = false;

    if (window.matchMedia("(prefers-reduced-motion: reduce)").matches) {
      AccordionDetails.DURATION = 0;
    }
  }

  ///////////////
  // Lifecyle
  ///////////////
  init() {
    this.bindEvents();
  }

  destroy() {
    super.destroy();

    this.unbindEvents();
  }

  ///////////////
  // Events
  ///////////////
  bindEvents() {
    this.$summary.addEventListener("click", this.onClick);
    window.addEventListener(CUSTOM_EVENT.MODAL_CLOSE, this.onModalClose);
  }

  unbindEvents() {
    this.$summary.removeEventListener("click", this.onClick);
    window.removeEventListener(CUSTOM_EVENT.MODAL_CLOSE, this.onModalClose);
  }

  ///////////////
  // Callbacks
  ///////////////
  onClick(e) {
    e.preventDefault();

    if (this.isClosing || !this.el.open) {
      this.open();
      this.isSingle && this.shrinkSiblings();
    } else if (this.isExpanding || this.el.open) {
      this.shrink();
    }
  }

  onAnimationFinish(open) {
    this.el.open = open;

    this.animation = null;

    this.isClosing = false;
    this.isExpanding = false;

    this.el.style.height = this.el.style.overflow = "";
  }

  onModalClose(e) {
    const $modal = e.detail;
    if ($modal.contains(this.el)) {
      this.shrink();
    }
  }

  ///////////////
  // Methods
  ///////////////
  shrink() {
    this.el.style.overflow = "hidden";
    this.isClosing = true;
    this.el.classList.remove(AccordionDetails.CLASS_ACTIVE);

    if (this.$parent)
      this.$parent.classList.remove(AccordionDetails.CLASS_ACTIVE);

    const startHeight = `${this.el.offsetHeight}px`;
    const endHeight = `${this.$summary.offsetHeight}px`;

    if (this.animation) {
      this.animation.cancel();
    }

    this.animation = this.el.animate(
      {
        height: [startHeight, endHeight],
      },
      {
        duration: AccordionDetails.DURATION,
        easing: AccordionDetails.EASING,
      }
    );

    this.animation.onfinish = () => this.onAnimationFinish(false);
    this.animation.oncancel = () => {
      this.isClosing = false;
      this.el.classList.add(AccordionDetails.CLASS_ACTIVE);
    };

    this.onShrink?.(this.el);
  }

  open() {
    this.el.style.overflow = "hidden";
    this.el.style.height = `${this.el.offsetHeight}px`;
    this.el.open = true;

    window.requestAnimationFrame(() => this.expand());

    this.onOpen?.(this.el);
  }

  expand() {
    this.isExpanding = true;
    this.el.classList.add(AccordionDetails.CLASS_ACTIVE);

    if (this.$parent) this.$parent.classList.add(AccordionDetails.CLASS_ACTIVE);

    const startHeight = `${this.el.offsetHeight}px`;
    const endHeight = `${
      this.$summary.offsetHeight + this.$content.offsetHeight
    }px`;

    if (this.animation) {
      this.animation.cancel();
    }

    this.animation = this.el.animate(
      {
        height: [startHeight, endHeight],
      },
      {
        duration: AccordionDetails.DURATION,
        easing: AccordionDetails.EASING,
      }
    );

    this.animation.onfinish = () => this.onAnimationFinish(true);
    this.animation.oncancel = () => {
      this.isExpanding = false;
      this.el.classList.remove(AccordionDetails.CLASS_ACTIVE);
    };
  }

  shrinkSiblings() {
    const $siblings = this.$group?.querySelectorAll("[data-module-accordion]");

    for (const $sibling of $siblings) {
      if ($sibling !== this.el && $sibling.open) {
        const moduleID = $sibling.dataset.moduleAccordion;
        moduleID && this.call("shrink", null, "AccordionDetails", moduleID);
      }
    }
  }
}
