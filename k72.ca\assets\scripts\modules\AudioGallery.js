import { default as Accordion } from "./Accordion";

export default class extends Accordion {
  constructor(m) {
    super(m);

    this.events.click["audio-button"] = "clickAudio";
  }

  init() {
    super.init();

    this.onAudioEndBind = this.onAudioEnd.bind(this);
    for (let audio of Array.from(this.$("audio"))) {
      audio.addEventListener("ended", this.onAudioEndBind);
    }
  }

  clickAudio(e) {
    e.preventDefault();

    const target = e.curTarget;
    const parent = target.parentNode;
    const label = this.$("audio-label", target)[0];
    const audio = this.$("audio", parent)[0];

    if (audio.paused) {
      audio.play();
      label.innerText = this.getData("label-pause", label);
    } else {
      audio.pause();
      label.innerText = this.getData("label-play", label);
    }

    if (audio.paused) parent.classList.remove("-playing");
    else parent.classList.add("-playing");
  }

  onAudioEnd(e) {
    this.stopAllAudio();
  }

  stopAllAudio() {
    for (let audio of Array.from(this.$("audio"))) {
      audio.pause();
      audio.currentTime = 0;
    }

    for (let audioButton of Array.from(this.$("audio-button"))) {
      const label = this.$("audio-label", audioButton)[0];
      label.innerText = this.getData("label-play", label);
      audioButton.parentNode.classList.remove("-playing");
    }
  }

  closeItem(item) {
    super.closeItem(item);
    this.stopAllAudio();
  }

  destroy() {
    super.destroy();

    for (let audio of Array.from(this.$("audio"))) {
      audio.removeEventListener("ended", this.onAudioEndBind);
    }
  }
}
