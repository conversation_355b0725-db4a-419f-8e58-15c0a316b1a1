import { module } from 'modujs';
import { Swiper } from '../lib/swiper';
import { gsap } from '../lib/gsap';

export default class extends module {
    constructor(m) {
        super(m);

        this.slideNext = true
    }

    init() {
        // Swiper.use([Lazy]);

        this.onUpdate = (swiper) => {
            for(let slide of swiper.slides) {
                const index = slide.getAttribute('data-swiper-slide-index')
                let counter = this.$('counter', slide)[0]
                counter.innerText = `${parseInt(index)+1}`.padStart(2, '0');
            }
        };

        this.swiper = new Swiper(this.el, {
            loop: true,
            loopedSlides: 2,
            spaceBetween: 0,
            slidesPerView: 'auto',
            breakpoints: {
                700: {
                    slidesPerGroup: 2
                }
            },
            on: {
                init: this.onUpdate
            },
            // lazy: {
            //     loadPrevNext: true
            // }
        })

        this.swiper.on('click', () => {

            if(this.slideNext) {
                this.swiper.slideNext()
            } else {
                this.swiper.slidePrev()
            }
        })

        this.handleCursor();
    }

    handleCursor() {
        this.cursor = this.$('cursor')[0]

        this.mouseEnterBind = () => this.cursor.classList.add('-visible')
        this.mouseLeaveBind = () => this.cursor.classList.remove('-visible')

        let bounds, x, y
        this.mouseMoveBind = e => {
            if(window.matchMedia('(hover: none)').matches) {
                this.slideNext = true
                return
            }

            bounds = this.el.getBoundingClientRect();
            x = e.clientX - bounds.left;
            y = e.clientY - bounds.top;

            gsap.set(this.cursor, { x, y })
            this.slideNext = x > bounds.width / 2
            this.cursor.classList.toggle('-rotate', !this.slideNext)
        }

        this.el.addEventListener('mouseenter', this.mouseEnterBind)
        this.el.addEventListener('mouseleave', this.mouseLeaveBind)
        this.el.addEventListener('mousemove', this.mouseMoveBind)
    }

    destroy() {
        super.destroy()

        this.el.removeEventListener('mouseenter', this.mouseEnterBind)
        this.el.removeEventListener('mouseleave', this.mouseLeaveBind)
        this.el.removeEventListener('mousemove', this.mouseMoveBind)

        this.swiper.detachEvents()
        this.swiper.destroy()
    }
}
