import { module } from 'modujs';
import { gsap } from '../lib/gsap';
import { html } from '../utils/environment';

const MIN_HEIGHT = 20;

export default class extends module {
    constructor(m) {
        super(m);
    }

    init() {
        if(!html.classList.contains('has-scroll-smooth')) {
            this.disabled = true;
            return;
        }

        this.items = Array.from(this.$('item'))
        this.outers = Array.from(this.$('outer'))
        this.inners = Array.from(this.$('inner'))

        this.compute();

        this.checkResizeBind = this.checkResize.bind(this)
        window.addEventListener('resize', this.checkResizeBind)

        // VISUAL HELPERS
        // let refLineTopDebug = document.createElement('div')
        // refLineTopDebug.style = `position: fixed; top: ${this.refLines.top}px; left: 0; width: 100%; height: 1px; background-color: chartreuse; z-index: 9999;`;
        // document.body.appendChild(refLineTopDebug)

        // let refLineBottomDebug = document.createElement('div')
        // refLineBottomDebug.style = `position: fixed; top: ${this.refLines.bottom}px; left: 0; width: 100%; height: 1px; background-color: chartreuse; z-index: 9999;`;
        // document.body.appendChild(refLineBottomDebug)
    }

    checkResize() {
        if(!this.resizeTick) {
            this.resizeTick = true
            requestAnimationFrame(() => {
                this.compute()
                this.resizeTick = false
            })
        }
    }

    compute() {
        gsap.set([...this.items, ...this.inners, ...this.outers], { clearProps: 'all' });
        gsap.set(this.el, { clearProps: 'height' })

        this.thumbBCR = this.items[0].getBoundingClientRect()
        // console.log(this.thumbBCR);

        this.marginBottom = parseInt(getComputedStyle(this.items[0]).getPropertyValue('margin-bottom'));
        this.nbOfItemsPerRow = this.getNbOfItemsPerRow();

        this.refLines = {
            top: (window.innerHeight/2) - (this.thumbBCR.height / 2),
            bottom: (window.innerHeight/2) + (this.thumbBCR.height / 2)
        }

        this.setContainerHeight()
        this.update();
        this.call('update', null, 'Scroll')
    }

    setContainerHeight() {
        this.el.style.height = getComputedStyle(this.el).getPropertyValue('height');
    }

    getNbOfItemsPerRow() {
        let i = 0;
        let stop = false;
        let lastCoord;

        while(!stop && i < this.items.length) {
            const coord = this.items[i].getBoundingClientRect().top

            if((lastCoord && lastCoord == coord) || !lastCoord) {
                lastCoord = coord;
                i++;
            } else {
                stop = true
            }
        }

        return i;
    }

    update() {
        if(this.disabled) return;

        let i = 0;
        for(let item of this.items) {
            const BCR = item.getBoundingClientRect();

            const distanceWithTopRefLine = (BCR.top) - (this.refLines.top)
            // const distanceWithBottomRefLine = (BCR.bottom) - (this.refLines.bottom)

            let scaleY = 0;
            // BELOW IDLE
            if(distanceWithTopRefLine >= 0) {
                scaleY = Math.abs(distanceWithTopRefLine) / (window.innerHeight - this.refLines.top)
            } else { // ABOVE IDLE
                // scaleY = Math.abs(distanceWithBottomRefLine) / (this.refLines.bottom)
            }

            gsap.set(this.outers[i], {
                height: Math.floor(Math.max(MIN_HEIGHT, this.thumbBCR.height * (1 - scaleY))),
                // alignItems: distanceWithTopRefLine > 0 && Math.abs(distanceWithTopRefLine) > Math.abs(distanceWithBottomRefLine) ? 'flex-start' : 'flex-end'
            })

            gsap.set(this.inners[i], { y: (-this.thumbBCR.height/2 * (scaleY)), force3D: true })

            // TRIGGER REFLOW
            // let test = document.documentElement.offsetHeight;
            i++;
        }
    }

    destroy() {
        super.destroy()

        window.removeEventListener('resize', this.checkResizeBind)
    }
}
