import { module } from 'modujs';

const COLS = 20

export default class extends module {
    constructor(m) {
        super(m);
    }

    init() {
        this.setStyle()
        this.populate()

        document.addEventListener('keydown', (e) => {
            if(e.key == 'Control') {
                if(this.el.style.visibility == 'hidden')
                    this.el.style.visibility = 'visible'
                else
                    this.el.style.visibility = 'hidden'
            }
        })
    }

    setStyle() {
        this.el.style.position = "fixed";
        this.el.style.top = "0";
        this.el.style.left = "0";
        this.el.style.width = "100%";
        this.el.style.height = "100%";
        this.el.style.zIndex = "9999";
        this.el.style.visibility = 'hidden'
        this.el.style.pointerEvents = "none";
    }

    populate() {
        this.el.innerHTML = '';

        for(let i = 0; i < COLS; i++) {
            let col = document.createElement('span')
            col.style.display = "inline-block";
            col.style.height = "100%";
            col.style.outline = "1px solid rgba(255, 0, 0,0.2)";

            if(i == 0)
                col.style.marginLeft = "0.625rem";

            col.style.marginRight = "0.625rem";

            col.style.width = `calc((100vw - (0.625rem*${COLS+1})) * 1/${COLS}`
            col.style.backgroundColor = "rgba(255, 0, 0,0.1)"
            this.el.appendChild(col)
        }
    }
}
