import { module } from 'modujs';

export default class extends module {
    constructor(m) {
        super(m);
    }

    init() {

        this.video = this.$('video')[0]

        setTimeout(() => {
            this.resumeVideo()
        }, 750);

        setTimeout(() => {
            this.call('play', null, 'Circle', 'home')
        }, 750);

        this.compute()
        this.checkResizeBind = this.checkResize.bind(this)
        window.addEventListener('resize', this.checkResizeBind)
    }

    checkResize() {
        if(!this.resizeTick) {
            this.resizeTick = true
            requestAnimationFrame(() => {
                this.compute()
                this.resizeTick = false
            })
        }
    }

    compute() {
        this.el.style.minHeight = window.innerHeight+'px';
    }

    pauseVideo() {
        this.video.pause()
    }

    resumeVideo() {
        this.video.play()
    }

    destroy() {
        super.destroy()
        window.removeEventListener('resize', this.checkResizeBind)
    }
}
