import { module } from 'modujs';
import { Swiper, EffectCreative } from '../lib/swiper';
import { gsap } from '../lib/gsap';

export default class extends module {
    constructor(m) {
        super(m);

        this.slideNext = true
    }

    init() {

        this.handleSwiper();
        this.handleCursor();
    }

    handleSwiper() {

        this.swiper = new Swiper(this.el, {
            loop: true,
            loopedSlides: 1,
            spaceBetween: 0,
            slidesPerView: 1,
            speed: 500,
            modules: [EffectCreative],
            effect: 'creative',
            creativeEffect: {
                prev: {
                    translate: ['-10%', 0, 0],
                },
                next: {
                    translate: ['100%', 0, 0],
                },
            }
        })

        this.swiper.on('click', () => {

            if(this.slideNext) {
                this.swiper.slideNext()
            } else {
                this.swiper.slidePrev()
            }
        })

        // this.swiper = new Swiper(this.el, {
        //     init: false,
        //     loop: true,
        //     loopedSlides: 1,
        //     threshold: 10,
        //     slidesPerView: 1,
        //     speed: 500,
        //     virtualTranslate: true,
        // })

        // this.swiper.on('init', (swiper) => {
        //     this.setTranslate()
        // })

        // // this.swiper.on('progress', (swiper, progress) => {
        // //     this.progress(progress);
        // // })

        // this.swiper.on('setTransition', (swiper, transition) => {
        //     this.setTransition(transition);
        // })

        // this.swiper.on('setTranslate', (swiper, translate) => {
        //     this.setTranslate();
        // })

        // this.swiper.on('click', () => {
        //     // console.log(event, this.nextOnClick);

        //     console.log(this.nextOnClick, this.swiper.activeIndex, this.swiper.slides.length)

        //     // dirty HACK to handle prev / next with virtualTranslate: true
        //     if(this.nextOnClick) {
        //         if(this.swiper.activeIndex === this.swiper.slides.length - 1) {
        //             this.swiper.slideToLoop(0)
        //         }
        //         else {
        //             console.log('next');
        //             this.swiper.slideToLoop(this.swiper.activeIndex + 1)
        //         }
        //     } else {
        //         if(this.swiper.activeIndex == 0) {
        //             // console.log('prev loop');
        //             this.swiper.slideToLoop(this.swiper.slides.length, false)
        //             this.swiper.slideToLoop(this.swiper.slides.length - 1)
        //         } else {
        //             console.log('prev');
        //             this.swiper.slideToLoop(this.swiper.activeIndex - 1)
        //         }
        //     }

        //     // // dirty HACK to handle prev / next with virtualTranslate: true
        //     // if(this.nextOnClick) {
        //     //     if(this.swiper.activeIndex == (this.swiper.slides.length - 1)) {
        //     //         // console.log('next loop');
        //     //         this.swiper.slideToLoop(0,false)
        //     //         this.swiper.slideToLoop(1)
        //     //     }
        //     //     else {
        //     //         // console.log('next');
        //     //         this.swiper.slideToLoop(this.swiper.activeIndex)
        //     //     }
        //     // } else {
        //     //     if(this.swiper.activeIndex == 0) {
        //     //         // console.log('prev loop');
        //     //         this.swiper.slideToLoop(this.swiper.slides.length - 3, false)
        //     //         this.swiper.slideToLoop(this.swiper.slides.length - 4)
        //     //     } else {
        //     //         // console.log('prev');
        //     //         this.swiper.slideToLoop(this.swiper.activeIndex-2)
        //     //     }
        //     // }
        // })

        // this.swiper.init()
    }

    // getTransitionSpeed() {
    //     const transitionSpeed = this.currentTransitionSpeed;
    //     // reset the variable for future calls
    //     this.currentTransitionSpeed = 0;
    //     return transitionSpeed;
    // }

    // setTransition(transitionSpeed) {
    //     this.currentTransitionSpeed = transitionSpeed;
    // }

    // setTranslate() {
    //     const durationInSeconds = this.getTransitionSpeed() / 1000;

    //     if(this.tl && this.tl.kill) this.tl.kill()
    //     this.tl = gsap.timeline({})

    //     // do magic with each slide
    //     for(let index = 0; index < this.swiper.slides.length; index++) {
    //     let x = -this.swiper.slides[index].swiperSlideOffset;
    //     x = Math.min(x, this.swiper.translate);

    //     const borderRadius = this.swiper.translate < -this.swiper.slides[index].swiperSlideOffset

    //     if(borderRadius) {
    //         this.swiper.slides[index].classList.toggle('-radius', borderRadius)
    //     }

    //     gsap.set(this.swiper.slides[index], { zIndex: this.swiper.slides.length - index })

    //     if(durationInSeconds) {
    //         this.tl.to(this.swiper.slides[index], {
    //             x,
    //             duration: durationInSeconds
    //         },0)
    //     } else {
    //         gsap.set(this.swiper.slides[index], {
    //             x
    //         });
    //     }
    //     };
    // }


    handleCursor() {
        this.cursor = this.$('cursor')[0]

        this.mouseEnterBind = () => this.cursor.classList.add('-visible')
        this.mouseLeaveBind = () => this.cursor.classList.remove('-visible')

        let bounds, x, y
        this.mouseMoveBind = e => {
            if(window.matchMedia('(hover: none)').matches) {
                this.slideNext = true
                return
            }

            bounds = this.el.getBoundingClientRect();
            x = e.clientX - bounds.left;
            y = e.clientY - bounds.top;

            gsap.set(this.cursor, { x, y })
            // this.slideNext = x > bounds.width / 2
            // this.cursor.classList.toggle('-rotate', !this.slideNext)
        }

        this.el.addEventListener('mouseenter', this.mouseEnterBind)
        this.el.addEventListener('mouseleave', this.mouseLeaveBind)
        this.el.addEventListener('mousemove', this.mouseMoveBind)
    }

    destroy() {
        super.destroy()

        this.el.removeEventListener('mouseenter', this.mouseEnterBind)
        this.el.removeEventListener('mouseleave', this.mouseLeaveBind)
        this.el.removeEventListener('mousemove', this.mouseMoveBind)

        this.swiper.detachEvents()
        this.swiper.destroy()
    }
}



// import { module } from 'modujs';
// import { gsap } from '../lib/gsap';
// import { Swiper } from '../lib/swiper';

// // inspired from this article: https://arnost.medium.com/creating-custom-slide-transitions-in-swiper-js-also-with-gsap-ac71f9badf53
// // but adapted to current swiper version (6.5.0)
// export default class extends module {
//     constructor(m) {
//         super(m);

//         this.currentTransitionSpeed = 0;
//         this.nextOnClick = true
//     }

//     init() {
//         this.handleCursor();
//         this.handleSwiper();
//     }

//     handleSwiper() {
//         this.swiper = new Swiper(this.el, {
//             init: false,
//             loop: true,
//             threshold: 10,
//             slidesPerView: 1,
//             speed: 500, // Set the speed of your animation in ms
//             // watchSlidesProgress: true, // enable the 'proress' property on each slide
//             virtualTranslate: true, // makes the slider not move automatically, you'll have to do the translate animation
//             //watchSlidesVisibility: true, // slides that in viewport will have additional visible class
//         })

//         this.swiper.on('init', (swiper) => {
//             this.setTranslate()
//         })

//         // this.swiper.on('progress', (swiper, progress) => {
//         //     this.progress(progress);
//         // })

//         this.swiper.on('setTransition', (swiper, transition) => {
//             this.setTransition(transition);
//         })

//         this.swiper.on('setTranslate', (swiper, translate) => {
//             this.setTranslate();
//         })

//         this.swiper.on('click', () => {
//             // console.log(event, this.nextOnClick);

//             console.log(this.nextOnClick, this.swiper.activeIndex, this.swiper.slides.length)

//             // dirty HACK to handle prev / next with virtualTranslate: true
//             if(this.nextOnClick) {
//                 if(this.swiper.activeIndex === this.swiper.slides.length - 1) {
//                     this.swiper.slideToLoop(0)
//                 }
//                 else {
//                     console.log('next');
//                     this.swiper.slideToLoop(this.swiper.activeIndex + 1)
//                 }
//             } else {
//                 if(this.swiper.activeIndex == 0) {
//                     // console.log('prev loop');
//                     this.swiper.slideToLoop(this.swiper.slides.length, false)
//                     this.swiper.slideToLoop(this.swiper.slides.length - 1)
//                 } else {
//                     console.log('prev');
//                     this.swiper.slideToLoop(this.swiper.activeIndex - 1)
//                 }
//             }

//             // // dirty HACK to handle prev / next with virtualTranslate: true
//             // if(this.nextOnClick) {
//             //     if(this.swiper.activeIndex == (this.swiper.slides.length - 1)) {
//             //         // console.log('next loop');
//             //         this.swiper.slideToLoop(0,false)
//             //         this.swiper.slideToLoop(1)
//             //     }
//             //     else {
//             //         // console.log('next');
//             //         this.swiper.slideToLoop(this.swiper.activeIndex)
//             //     }
//             // } else {
//             //     if(this.swiper.activeIndex == 0) {
//             //         // console.log('prev loop');
//             //         this.swiper.slideToLoop(this.swiper.slides.length - 3, false)
//             //         this.swiper.slideToLoop(this.swiper.slides.length - 4)
//             //     } else {
//             //         // console.log('prev');
//             //         this.swiper.slideToLoop(this.swiper.activeIndex-2)
//             //     }
//             // }
//         })

//         this.swiper.init()
//     }

//     handleCursor() {
//         this.cursor = this.$('cursor')[0]

//         this.mouseEnterBind = (e) => {
//             this.cursor.classList.add('-visible')
//         }
//         this.el.addEventListener('mouseenter', this.mouseEnterBind)

//         this.mouseLeaveBind = (e) => {
//             this.cursor.classList.remove('-visible')
//         }
//         this.el.addEventListener('mouseleave', this.mouseLeaveBind)

//         let bounds, x, y
//         this.mouseMoveBind = (e) => {
//             if(window.matchMedia('(hover: none)').matches) {
//                 this.nextOnClick = true
//                 return
//             }

//             bounds = this.el.getBoundingClientRect();
//             x = e.clientX - bounds.left;
//             y = e.clientY - bounds.top;

//             gsap.set(this.cursor, { x, y })
//             this.nextOnClick = x > bounds.width / 2

//             this.cursor.classList.toggle('-rotate', !this.nextOnClick)
//         }
//         this.el.addEventListener('mousemove', this.mouseMoveBind)
//     }

//     getTransitionSpeed() {
//       const transitionSpeed = this.currentTransitionSpeed;
//       // reset the variable for future calls
//       this.currentTransitionSpeed = 0;
//       return transitionSpeed;
//     }

//     setTransition(transitionSpeed) {
//         this.currentTransitionSpeed = transitionSpeed;
//     }

//     setTranslate() {
//       const durationInSeconds = this.getTransitionSpeed() / 1000;

//       if(this.tl && this.tl.kill) this.tl.kill()
//       this.tl = gsap.timeline({})

//       // do magic with each slide
//       for(let index = 0; index < this.swiper.slides.length; index++) {
//         let x = -this.swiper.slides[index].swiperSlideOffset;
//         x = Math.min(x, this.swiper.translate);

//         const borderRadius = this.swiper.translate < -this.swiper.slides[index].swiperSlideOffset

//         if(borderRadius) {
//             this.swiper.slides[index].classList.toggle('-radius', borderRadius)
//         }

//         gsap.set(this.swiper.slides[index], { zIndex: this.swiper.slides.length - index })

//         if(durationInSeconds) {
//             this.tl.to(this.swiper.slides[index], {
//                 x,
//                 duration: durationInSeconds
//             },0)
//         } else {
//             gsap.set(this.swiper.slides[index], {
//                 x
//             });
//         }
//       };
//     }

//     // progress(progress) {
//     // }

//     destroy() {
//         super.destroy();

//         this.el.removeEventListener('mouseenter', this.mouseEnterBind)
//         this.el.removeEventListener('mouseleave', this.mouseLeaveBind)
//         this.el.removeEventListener('mousemove', this.mouseMoveBind)

//         this.swiper.detachEvents()
//         this.swiper.destroy()
//     }
// }
