import { module } from 'modujs';
import { gsap } from '../lib/gsap';

const HOVER_DURATION = .25;

export default class extends module {
    constructor(m) {
        super(m);

        this.links = Array.from(this.$('link'))
    }

    init() {
        this.handleInteractions()
    }

    handleInteractions() {
        this.onLinkEnterBind = this.onLinkEnter.bind(this);
        this.onLinkLeaveBind = this.onLinkLeave.bind(this);

        for(let link of this.links) {
            link.addEventListener('mouseenter', this.onLinkEnterBind)
            link.addEventListener('mouseleave', this.onLinkLeaveBind)
            link.addEventListener('focus', this.onLinkEnterBind)
            link.addEventListener('blur', this.onLinkLeaveBind)
        }
    }

    onLinkEnter(e) {
        if (!matchMedia('(pointer:fine)').matches) {
          return;
        }

        const target = e.currentTarget
        const overlay = target.children[1]
        const overlayInner = overlay.children[0]

        let way;
        if(e.clientY > window.cursorPosition.y) {
            way = 'down'
        } else if (e.clientY < window.cursorPosition.y) {
            way = 'up'
        } else {
            const BCR = target.getBoundingClientRect()
            if(e.layerY > BCR.height/2) {
                way = 'up'
            } else {
                way = 'down'
            }
        }
        // console.log('enter', way, target);

        if(target.tl && target.tl.kill) target.tl.kill()
        target.tl = gsap.timeline({})
        target.tl.set(overlay, { visibility: 'visible' }, 0)
        target.tl.fromTo(overlay, { y: way == 'down' ? '-100%' : '100%' }, { y: '0%', duration: HOVER_DURATION, ease: 'power2.out' }, 0)
        target.tl.fromTo(overlayInner, { y: way == 'down' ? '100%' : '-100%' }, { y: '0%', duration: HOVER_DURATION, ease: 'power2.out' }, 0)
    }

    onLinkLeave(e) {
        if (!matchMedia('(pointer:fine)').matches) {
          return;
        }

        const target = e.currentTarget
        const overlay = target.children[1]
        const overlayInner = overlay.children[0]

        let way;
        if(e.clientY > window.cursorPosition.y) {
            way = 'down'
        } else if (e.clientY < window.cursorPosition.y) {
            way = 'up'
        } else {
            const BCR = target.getBoundingClientRect()
            if(e.layerY > BCR.height/2) {
                way = 'down'
            } else {
                way = 'up'
            }
        }
        // console.log('leave', way, target);

        if(target.tl && target.tl.kill) target.tl.kill()
        target.tl = gsap.timeline({})
        target.tl.to(overlay, { y: way == 'down' ? '100%' : '-100%', duration: HOVER_DURATION, ease: 'power2.out' }, 0)
        target.tl.to(overlayInner, { y: way == 'down' ? '-100%' : '100%', duration: HOVER_DURATION, ease: 'power2.out' }, 0)
        target.tl.set(overlay, { clearProps: 'visibility' })
    }

    destroy() {
        for(let link of this.links) {
            link.removeEventListener('mouseenter', this.onLinkEnterBind)
            link.removeEventListener('mouseleave', this.onLinkLeaveBind)
            link.removeEventListener('focus', this.onLinkEnterBind)
            link.removeEventListener('blur', this.onLinkLeaveBind)
        }
    }
}
