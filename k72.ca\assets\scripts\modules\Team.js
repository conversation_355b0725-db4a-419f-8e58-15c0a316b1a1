import { module } from 'modujs';

export default class extends module {
    constructor(m) {
        super(m);

        this.featured = this.$('featured')[0];
        this.visuals = this.$('visuals')[0];
        this.visualsZIndex = 1;
    }

    init() {
        this.listMembers = Array.from(this.$('member'))

        this.orderMembers();
        this.handleInteractions();

        // console.log(this);
    }

    orderMembers() {
        this.extractFeaturedMembers();
        this.renderFeaturedMembers();
        this.call('update', null, 'Scroll')
    }

    extractFeaturedMembers() {
        this.featuredMembers = []
        for(let i = 0; i < 2; i++) {
            let randomIndex = Math.floor(Math.random()*(this.listMembers.length))
            let pickedMember = this.listMembers.splice(randomIndex, 1).map(el => {
                const id = this.getData('id', el);
                const firstname = this.$('firstname', el)[0].innerText;
                const lastname = this.$('lastname', el)[0].innerText;
                const position = this.$('position', el)[0].innerText;
                const image = this.getData('image', el);
                el.remove()
                return { id, firstname, lastname, position, image}
            })[0]
            this.featuredMembers.push(pickedMember);
        }
    }

    renderFeaturedMembers() {
        for(let i = 0; i < this.featuredMembers.length; i++) {
            const member = this.featuredMembers[i];

            this.featured.innerHTML+=`
            <div class="c-team_featured_member">
                <div class="c-team_featured_member_sticky" id="featured-member-${member.id}-sticky"></div>
                <div class="c-team_featured_member_content" data-scroll data-scroll-repeat data-scroll-offset="40%,60%">
                    <div class="c-team_featured_member_line">
                        <span class="c-team_featured_member_line_inner">
                            <span class="c-team_featured_member_name">${member.firstname}</span>
                        </span>
                        <span class="c-team_featured_member_line_inner">
                            <span class="c-team_featured_member_name">${member.firstname}</span>
                        </span>
                    </div>
                    <div class="c-team_featured_member_line">
                        <span class="c-team_featured_member_line_inner">
                            <span class="c-team_featured_member_name">${member.lastname}</span>
                            <span class="c-team_featured_member_position">${member.position}</span>
                        </span>
                        <span class="c-team_featured_member_line_inner">
                            <span class="c-team_featured_member_name">${member.lastname}</span>
                            <span class="c-team_featured_member_position">${member.position}</span>
                        </span>
                    </div>
                </div>
                <div class="c-team_featured_member_image-wrapper" data-scroll data-scroll-repeat data-scroll-sticky data-scroll-target="#featured-member-${member.id}-sticky">
                    <figure class="c-team_featured_member_image" style="background-image: url(${member.image});">
                        <img src="${member.image}" alt="" />
                    </figure>
                </div>
            </div>
            `
        }
    }

    handleInteractions() {
        this.onListMemberLeaveBind = (e) => {
            this.visuals.classList.remove('is-active')
        }

        this.onListMemberEnterBind = (e) => {
            const target = e.currentTarget
            const id = this.getData('id', target)
            this.showListImageById(id)
            this.visuals.classList.add('is-active')
        }

        for(let el of this.listMembers) {
            el.addEventListener('mouseleave', this.onListMemberLeaveBind)
            el.addEventListener('blur', this.onListMemberLeaveBind)
            el.addEventListener('mouseenter', this.onListMemberEnterBind)
            el.addEventListener('focus', this.onListMemberEnterBind)
        }
    }

    showListImageById(id) {
        let old = this.visuals.querySelector('.is-current')
        if(old) old.classList.remove('is-current');

        let image = this.$(`list-image-${id}`, this.visuals)[0];
        image.classList.add('is-current')

        // trick to restart animation
        image.classList.remove('run-animation')
        void image.offsetWidth;
        image.classList.add('run-animation')

        // stackup
        this.visualsZIndex++;
        image.style.zIndex = this.visualsZIndex
    }

    destroy() {
        for(let el of this.listMembers) {
            el.removeEventListener('mouseleave', this.onListMemberLeaveBind)
            el.removeEventListener('blur', this.onListMemberLeaveBind)
            el.removeEventListener('mouseenter', this.onListMemberEnterBind)
            el.removeEventListener('focus', this.onListMemberEnterBind)
        }
    }
}
