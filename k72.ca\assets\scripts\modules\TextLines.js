import { module } from 'modujs';
import { gsap, SplitText } from '../lib/gsap';

gsap.registerPlugin(SplitText);

export default class extends module {
    constructor(m) {
        super(m);
    }

    init() {
        this.compute()

        this.checkResizeBind = this.checkResize.bind(this)
        window.addEventListener('resize', this.checkResizeBind)
    }

    checkResize() {
        if(!this.resizeTick) {
            this.resizeTick = true
            requestAnimationFrame(() => {
                this.compute()
                this.resizeTick = false
            })
        }
    }

    compute() {
        this.destroyEvents();

        if(this.split) {
            this.split.revert()
            this.split = null;
        }

        this.split = new SplitText(this.el, { type: 'lines', linesClass: 'c-text-lines_item' })
        this.el.classList.add('c-text-lines')

        for(let line of this.split.lines) {
            if(this.absolute) {
                line.style.width = 'auto'
                line.style.height = 'auto'
            }
            line.innerHTML = `<span class="c-text-lines_item_inner">${line.innerHTML}</span>`
        }

        this.initEvents()
        this.call('update', null, 'Scroll')
    }

    initEvents() {
        this.scrollToClickBind = (e) => {
            e.preventDefault();

            this.call('scrollTo', {
                target: e.currentTarget.getAttribute(`data-scroll-href`) || e.currentTarget.getAttribute('href'),
                offset: e.currentTarget.getAttribute(`data-scroll-offset`)
            }, 'Scroll');
        }
        for(let scrollTo of Array.from(this.el.querySelectorAll('[data-scroll-to]'))) {
            scrollTo.addEventListener('click', this.scrollToClickBind);
        }
    }

    destroyEvents() {
        for(let scrollTo of Array.from(this.el.querySelectorAll('[data-scroll-to]'))) {
            scrollTo.removeEventListener('click', this.scrollToClickBind);
        }
    }

    destroy() {
        super.destroy()
        this.destroyEvents();
        window.removeEventListener('resize', this.checkResizeBind)
    }
}
