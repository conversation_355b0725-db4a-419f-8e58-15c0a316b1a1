import { module } from 'modujs';

export default class extends module {
    constructor(m) {
        super(m);

        this.events = {
            'mouseenter': {
                'button': 'onMouseEnter'
            },
            'mouseleave': {
                'button': 'onMouseLeave'
            },
            'focus': {
                'button': 'onMouseEnter'
            },
            'blur': {
                'button': 'onMouseLeave'
            },
        }

        this.videoPreview = this.$('video-preview')[0]
    }

    onMouseEnter() {
        this.el.classList.add('-hover')
        if(this.videoPreview && this.videoPreview.play) this.videoPreview.play();
    }

    onMouseLeave() {
        this.el.classList.remove('-hover')
        if(this.videoPreview && this.videoPreview.pause) this.videoPreview.pause();
    }
}
