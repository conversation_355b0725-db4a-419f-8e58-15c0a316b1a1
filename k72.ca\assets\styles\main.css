@charset "UTF-8";
/*! normalize.css v8.0.1 | MIT License | github.com/necolas/normalize.css */
/* Document
   ========================================================================== */
/**
 * 1. Correct the line height in all browsers.
 * 2. Prevent adjustments of font size after orientation changes in iOS.
 */
html {
  line-height: 1.15; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
}

/* Sections
   ========================================================================== */
/**
 * Remove the margin in all browsers.
 */
body {
  margin: 0;
}

/**
 * Render the `main` element consistently in IE.
 */
main {
  display: block;
}

/**
 * Correct the font size and margin on `h1` elements within `section` and
 * `article` contexts in Chrome, Firefox, and Safari.
 */
h1 {
  font-size: 2em;
  margin: 0.67em 0;
}

/* Grouping content
   ========================================================================== */
/**
 * 1. Add the correct box sizing in Firefox.
 * 2. Show the overflow in Edge and IE.
 */
hr {
  box-sizing: content-box; /* 1 */
  height: 0; /* 1 */
  overflow: visible; /* 2 */
}

/**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */
pre {
  font-family: monospace, monospace; /* 1 */
  font-size: 1em; /* 2 */
}

/* Text-level semantics
   ========================================================================== */
/**
 * Remove the gray background on active links in IE 10.
 */
a {
  background-color: transparent;
}

/**
 * 1. Remove the bottom border in Chrome 57-
 * 2. Add the correct text decoration in Chrome, Edge, IE, Opera, and Safari.
 */
abbr[title] {
  border-bottom: none; /* 1 */
  text-decoration: underline; /* 2 */
  -webkit-text-decoration: underline dotted;
  text-decoration: underline dotted; /* 2 */
}

/**
 * Add the correct font weight in Chrome, Edge, and Safari.
 */
b,
strong {
  font-weight: bolder;
}

/**
 * 1. Correct the inheritance and scaling of font size in all browsers.
 * 2. Correct the odd `em` font sizing in all browsers.
 */
code,
kbd,
samp {
  font-family: monospace, monospace; /* 1 */
  font-size: 1em; /* 2 */
}

/**
 * Add the correct font size in all browsers.
 */
small {
  font-size: 80%;
}

/**
 * Prevent `sub` and `sup` elements from affecting the line height in
 * all browsers.
 */
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/* Embedded content
   ========================================================================== */
/**
 * Remove the border on images inside links in IE 10.
 */
img {
  border-style: none;
}

/* Forms
   ========================================================================== */
/**
 * 1. Change the font styles in all browsers.
 * 2. Remove the margin in Firefox and Safari.
 */
button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-size: 100%; /* 1 */
  line-height: 1.15; /* 1 */
  margin: 0; /* 2 */
}

/**
 * Show the overflow in IE.
 * 1. Show the overflow in Edge.
 */
button,
input {
  /* 1 */
  overflow: visible;
}

/**
 * Remove the inheritance of text transform in Edge, Firefox, and IE.
 * 1. Remove the inheritance of text transform in Firefox.
 */
button,
select {
  /* 1 */
  text-transform: none;
}

/**
 * Correct the inability to style clickable types in iOS and Safari.
 */
button,
[type="button"],
[type="reset"],
[type="submit"] {
  -webkit-appearance: button;
}

/**
 * Remove the inner border and padding in Firefox.
 */
button::-moz-focus-inner,
[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner {
  border-style: none;
  padding: 0;
}

/**
 * Restore the focus styles unset by the previous rule.
 */
button:-moz-focusring,
[type="button"]:-moz-focusring,
[type="reset"]:-moz-focusring,
[type="submit"]:-moz-focusring {
  outline: 1px dotted ButtonText;
}

/**
 * Correct the padding in Firefox.
 */
fieldset {
  padding: 0.35em 0.75em 0.625em;
}

/**
 * 1. Correct the text wrapping in Edge and IE.
 * 2. Correct the color inheritance from `fieldset` elements in IE.
 * 3. Remove the padding so developers are not caught out when they zero out
 *    `fieldset` elements in all browsers.
 */
legend {
  box-sizing: border-box; /* 1 */
  color: inherit; /* 2 */
  display: table; /* 1 */
  max-width: 100%; /* 1 */
  padding: 0; /* 3 */
  white-space: normal; /* 1 */
}

/**
 * Add the correct vertical alignment in Chrome, Firefox, and Opera.
 */
progress {
  vertical-align: baseline;
}

/**
 * Remove the default vertical scrollbar in IE 10+.
 */
textarea {
  overflow: auto;
}

/**
 * 1. Add the correct box sizing in IE 10.
 * 2. Remove the padding in IE 10.
 */
[type="checkbox"],
[type="radio"] {
  box-sizing: border-box; /* 1 */
  padding: 0; /* 2 */
}

/**
 * Correct the cursor style of increment and decrement buttons in Chrome.
 */
[type="number"]::-webkit-inner-spin-button,
[type="number"]::-webkit-outer-spin-button {
  height: auto;
}

/**
 * 1. Correct the odd appearance in Chrome and Safari.
 * 2. Correct the outline style in Safari.
 */
[type="search"] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/**
 * Remove the inner padding in Chrome and Safari on macOS.
 */
[type="search"]::-webkit-search-decoration {
  -webkit-appearance: none;
}

/**
 * 1. Correct the inability to style clickable types in iOS and Safari.
 * 2. Change font properties to `inherit` in Safari.
 */
::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/* Interactive
   ========================================================================== */
/*
 * Add the correct display in Edge, IE 10+, and Firefox.
 */
details {
  display: block;
}

/*
 * Add the correct display in all browsers.
 */
summary {
  display: list-item;
}

/* Misc
   ========================================================================== */
/**
 * Add the correct display in IE 10+.
 */
template {
  display: none;
}

/**
 * Add the correct display in IE 10.
 */
[hidden] {
  display: none;
}

html {
  box-sizing: border-box;
}

template,
[hidden] {
  display: none;
}

*,
:before,
:after {
  box-sizing: inherit;
}

address {
  font-style: inherit;
}

dfn,
cite,
em,
i {
  font-style: italic;
}

b,
strong {
  font-weight: 700;
}

a {
  text-decoration: none;
}
a svg {
  pointer-events: none;
}

ul,
ol {
  margin: 0;
  padding: 0;
  list-style: none;
}

p,
figure {
  margin: 0;
  padding: 0;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: 1rem;
  margin: 0;
  font-weight: normal;
}

/**
 * 1. Single taps should be dispatched immediately on clickable elements
 */
a,
area,
button,
input,
label,
select,
textarea,
[tabindex] {
  /* [1] */
  touch-action: manipulation;
}

[hreflang] > abbr[title] {
  text-decoration: none;
}

table {
  border-spacing: 0;
  border-collapse: collapse;
}

hr {
  display: block;
  margin: 1em 0;
  padding: 0;
  height: 1px;
  border: 0;
  border-top: 1px solid #cccccc;
}

audio,
canvas,
iframe,
img,
svg,
video {
  vertical-align: middle; /* [1] */
}

audio:not([controls]) {
  display: none;
  height: 0;
}

img,
svg {
  max-width: 100%; /* [2] */
  height: auto;
}
img[width],
img[height],
svg[width],
svg[height] {
  /* [4] */
  max-width: none;
}

img {
  font-style: italic; /* [4] */
}

svg {
  fill: currentColor; /* [5] */
}

input,
select,
textarea {
  display: block;
  margin: 0;
  padding: 0;
  width: 100%;
  outline: 0;
  border: 0;
  border-radius: 0;
  background: none transparent;
  color: inherit;
  font: inherit;
  line-height: normal;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

select {
  text-transform: none;
}
select::-ms-expand {
  display: none;
}
select::-ms-value {
  background: none;
  color: inherit;
}

textarea {
  overflow: auto;
  resize: vertical;
}

button,
.c-button {
  display: inline-block; /* [1] */
  overflow: visible; /* [2] */
  margin: 0; /* [3] */
  padding: 0;
  outline: 0;
  border: 0;
  background: none transparent;
  color: inherit;
  vertical-align: middle; /* [4] */
  text-align: center; /* [3] */
  text-decoration: none;
  text-transform: none;
  font: inherit; /* [5] */
  line-height: normal;
  cursor: pointer; /* [6] */
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
button:focus,
button:hover,
.c-button:focus,
.c-button:hover {
  text-decoration: none;
}

@font-face {
  font-display: swap;
  font-family: "Lausanne";
  src: url("../fonts/Lausanne-300.woff2") format("woff2"),
    url("../fonts/Lausanne-300.woff") format("woff");
  font-weight: 300;
  font-style: normal;
}
@font-face {
  font-display: swap;
  font-family: "Lausanne";
  src: url("../fonts/Lausanne-500.woff2") format("woff2"),
    url("../fonts/Lausanne-500.woff") format("woff");
  font-weight: 500;
  font-style: normal;
}
html {
  min-height: 100%; /* [3] */
  color: #000000;
  font-family: "Lausanne", -apple-system, BlinkMacSystemFont, avenir next,
    avenir, segoe ui, helvetica neue, helvetica, Cantarell, Ubuntu, roboto, noto,
    arial, sans-serif;
  line-height: 1.5; /* [2] */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #ffffff;
  transition: color 0.3s cubic-bezier(0.215, 0.61, 0.355, 1),
    background-color 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
@media (max-width: 699px) {
  html {
    font-size: 14px;
  }
}
@media (min-width: 700px) and (max-width: 999px) {
  html {
    font-size: 14px;
  }
}
@media (min-width: 1000px) and (max-width: 1199px) {
  html {
    font-size: 15px;
  }
}
@media (min-width: 1200px) and (max-width: 1599px) {
  html {
    font-size: 16px; /* [2] */
  }
}
@media (min-width: 1600px) and (max-width: 1999px) {
  html {
    font-size: 17px;
  }
}
@media (min-width: 2000px) and (max-width: 2399px) {
  html {
    font-size: 18px;
  }
}
@media (min-width: 2400px) {
  html {
    font-size: 20px;
  }
}
html.is-loading {
  cursor: wait;
}
html.has-scroll-smooth {
  overflow: hidden;
}
html.has-scroll-dragging {
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
html[data-theme="dark"] {
  background-color: #000000;
  color: #ffffff;
}

.has-scroll-smooth body {
  overflow: hidden;
}

::-moz-selection {
  background-color: #d3fd50;
  color: #000000;
  text-shadow: none;
}

::selection {
  background-color: #d3fd50;
  color: #000000;
  text-shadow: none;
}

a {
  color: currentColor;
}
a:focus,
a:hover {
  color: currentColor;
}

.o-scroll {
  min-height: 100vh;
}

.o-container,
.c-preview-bar_inner {
  margin-right: auto;
  margin-left: auto;
  padding-right: 0.625rem;
  padding-left: 0.625rem;
}
@media (max-width: 699px) {
  .o-container,
  .c-preview-bar_inner {
    padding-right: 0.625rem;
    padding-left: 0.625rem;
  }
}
@media (min-width: 1000px) {
  .o-container.-medium,
  .-medium.c-preview-bar_inner,
  .o-container.c-video-gallery_head,
  .c-video-gallery_head.c-preview-bar_inner,
  .o-container.c-caption-gallery_text,
  .c-caption-gallery_text.c-preview-bar_inner,
  .o-container.c-caption-gallery_index,
  .c-caption-gallery_index.c-preview-bar_inner,
  .o-container.c-project-summary_list,
  .c-project-summary_list.c-preview-bar_inner,
  .o-container.c-project_year,
  .c-project_year.c-preview-bar_inner {
    padding-left: calc((100vw - 13.125rem) * 2 / 20 + 1.875rem);
    padding-right: calc((100vw - 13.125rem) * 2 / 20 + 1.875rem);
  }
}
@media (max-width: 999px) and (min-width: 700px) {
  .o-container.-medium,
  .-medium.c-preview-bar_inner,
  .o-container.c-video-gallery_head,
  .c-video-gallery_head.c-preview-bar_inner,
  .o-container.c-caption-gallery_text,
  .c-caption-gallery_text.c-preview-bar_inner,
  .o-container.c-caption-gallery_index,
  .c-caption-gallery_index.c-preview-bar_inner,
  .o-container.c-project-summary_list,
  .c-project-summary_list.c-preview-bar_inner,
  .o-container.c-project_year,
  .c-project_year.c-preview-bar_inner {
    padding-right: 2.5rem;
    padding-left: 2.5rem;
  }
}
@media (min-width: 1000px) {
  .o-container.-small,
  .-small.c-preview-bar_inner,
  .o-container.c-footer_legals_link,
  .c-footer_legals_link.c-preview-bar_inner {
    padding-left: calc((100vw - 13.125rem) * 6 / 20 + 4.375rem);
    padding-right: calc((100vw - 13.125rem) * 6 / 20 + 4.375rem);
  }
}
@media (max-width: 999px) and (min-width: 700px) {
  .o-container.-small,
  .-small.c-preview-bar_inner,
  .o-container.c-footer_legals_link,
  .c-footer_legals_link.c-preview-bar_inner {
    padding-right: 2.5rem;
    padding-left: 2.5rem;
  }
}

/**
 * Create ratio-bound content blocks, to keep media (e.g. images, videos) in
 * their correct aspect ratios.
 *
 * http://alistapart.com/article/creating-intrinsic-ratios-for-video
 *
 * 1. Default cropping is a 1:1 ratio (i.e. a perfect square).
 */
.o-ratio {
  position: relative;
  display: block;
  overflow: hidden;
}
.o-ratio:before {
  display: block;
  padding-bottom: 100%; /* [1] */
  width: 100%;
  content: "";
}

.o-ratio_content,
.o-ratio > img,
.o-ratio > iframe,
.o-ratio > embed,
.o-ratio > object {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  width: 100%;
}

.o-layout {
  margin: 0;
  padding: 0;
  list-style: none;
  font-size: 0;
  margin-left: 0;
}
.o-layout.-gutter {
  margin-left: -2.5rem;
}
.o-layout.-gutter-small {
  margin-left: -0.625rem;
}
.o-layout.-center {
  text-align: center;
}
.o-layout.-right {
  text-align: right;
}
.o-layout.-reverse {
  direction: rtl;
}
.o-layout.-reverse.-flex {
  flex-direction: row-reverse;
}
.o-layout.-flex {
  display: flex;
}
.o-layout.-flex.-top {
  align-items: flex-start;
}
.o-layout.-flex.-middle {
  align-items: center;
}
.o-layout.-flex.-bottom {
  align-items: flex-end;
}
.o-layout.-stretch {
  align-items: stretch;
}

.o-layout_item {
  display: inline-block;
  width: 100%;
  vertical-align: top;
  font-size: 1rem;
  padding-left: 0;
}
.o-layout.-gutter > .o-layout_item {
  padding-left: 2.5rem;
}
.o-layout.-gutter-small > .o-layout_item {
  padding-left: 0.625rem;
}
.o-layout.-middle > .o-layout_item {
  vertical-align: middle;
}
.o-layout.-bottom > .o-layout_item {
  vertical-align: bottom;
}
.o-layout.-center > .o-layout_item,
.o-layout.-right > .o-layout_item,
.o-layout.-reverse > .o-layout_item {
  text-align: left;
}
.o-layout.-reverse > .o-layout_item {
  direction: ltr;
}

.o-text.-micro,
.-micro.c-video-gallery_head,
.-micro.c-caption-gallery_text,
.-micro.c-caption-gallery_index,
.-micro.c-block-text,
.c-project-summary_text p.-micro,
.-micro.c-project-summary_list,
.-micro.c-project_year,
.-micro.c-footer_legals_link,
.c-menu_footer_legals_link {
  font-size: 0.6875rem;
}
.o-text.-small,
.-small.c-video-gallery_head,
.-small.c-caption-gallery_text,
.-small.c-caption-gallery_index,
.-small.c-block-text,
.c-project-summary_text p.-small,
.-small.c-project-summary_list,
.-small.c-project_year,
.c-footer_legals_link,
.-small.c-menu_footer_legals_link {
  font-size: 0.875rem;
}
.o-text.-medium,
.c-video-gallery_head,
.c-caption-gallery_text,
.c-caption-gallery_index,
.-medium.c-block-text,
.c-project-summary_text p.-medium,
.c-project-summary_text p.c-video-gallery_head,
.c-project-summary_text p.c-caption-gallery_text,
.c-project-summary_text p.c-caption-gallery_index,
.c-project-summary_list,
.c-project_year,
.-medium.c-footer_legals_link,
.-medium.c-menu_footer_legals_link {
  font-size: 1.25rem;
  line-height: 1.25;
}
.o-text.-large,
.-large.c-video-gallery_head,
.-large.c-caption-gallery_text,
.-large.c-caption-gallery_index,
.c-block-text,
.c-project-summary_text p,
.-large.c-project-summary_list,
.-large.c-project_year,
.-large.c-footer_legals_link,
.-large.c-menu_footer_legals_link {
  font-size: 3.5rem;
  line-height: 1;
}
@media (max-width: 1199px) {
  .o-text.-large,
  .-large.c-video-gallery_head,
  .-large.c-caption-gallery_text,
  .-large.c-caption-gallery_index,
  .c-block-text,
  .c-project-summary_text p,
  .-large.c-project-summary_list,
  .-large.c-project_year,
  .-large.c-footer_legals_link,
  .-large.c-menu_footer_legals_link {
    font-size: 2.8125rem;
  }
}
@media (max-width: 699px) {
  .o-text.-large,
  .-large.c-video-gallery_head,
  .-large.c-caption-gallery_text,
  .-large.c-caption-gallery_index,
  .c-block-text,
  .c-project-summary_text p,
  .-large.c-project-summary_list,
  .-large.c-project_year,
  .-large.c-footer_legals_link,
  .-large.c-menu_footer_legals_link {
    font-size: 1.5rem;
  }
}

.o-wysiwyg p {
  margin-bottom: 1em;
}
.o-wysiwyg h1,
.o-wysiwyg h2,
.o-wysiwyg h3,
.o-wysiwyg h4,
.o-wysiwyg h5,
.o-wysiwyg h6 {
  font-weight: 700;
  margin-top: 2em;
  margin-bottom: 0.5em;
}
.o-wysiwyg h1:first-child,
.o-wysiwyg h2:first-child,
.o-wysiwyg h3:first-child,
.o-wysiwyg h4:first-child,
.o-wysiwyg h5:first-child,
.o-wysiwyg h6:first-child {
  margin-top: 0;
}
.o-wysiwyg h1 {
  font-size: 1.875rem;
}
.o-wysiwyg h2 {
  font-size: 1.5rem;
}
.o-wysiwyg h3 {
  font-size: 1.25rem;
}
.o-wysiwyg h4 {
  font-size: 1.125rem;
}
.o-wysiwyg h5 {
  font-size: 1rem;
}
.o-wysiwyg h6 {
  font-size: 1rem;
  font-weight: 300;
  font-style: italic;
}
.o-wysiwyg a {
  text-decoration: underline;
}
.o-wysiwyg a:focus,
.o-wysiwyg a:hover {
  opacity: 0.5;
}

/**
 * Swiper 11.2.8
 * Most modern mobile touch slider and framework with hardware accelerated transitions
 * https://swiperjs.com
 *
 * Copyright 2014-2025 Vladimir Kharlampidi
 *
 * Released under the MIT License
 *
 * Released on: May 23, 2025
 */
/* FONT_START */
@font-face {
  font-family: "swiper-icons";
  src: url("data:application/font-woff;charset=utf-8;base64, 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");
  font-weight: 400;
  font-style: normal;
}
/* FONT_END */
:root {
  --swiper-theme-color: #007aff;
  /*
  --swiper-preloader-color: var(--swiper-theme-color);
  --swiper-wrapper-transition-timing-function: initial;
  */
}

:host {
  position: relative;
  display: block;
  margin-left: auto;
  margin-right: auto;
  z-index: 1;
}

.swiper {
  margin-left: auto;
  margin-right: auto;
  position: relative;
  overflow: hidden;
  list-style: none;
  padding: 0;
  /* Fix of Webkit flickering */
  z-index: 1;
  display: block;
}

.swiper-vertical > .swiper-wrapper {
  flex-direction: column;
}

.swiper-wrapper {
  position: relative;
  width: 100%;
  height: 100%;
  z-index: 1;
  display: flex;
  transition-property: transform;
  transition-timing-function: var(
    --swiper-wrapper-transition-timing-function,
    initial
  );
  box-sizing: content-box;
}

.swiper-android .swiper-slide,
.swiper-ios .swiper-slide,
.swiper-wrapper {
  transform: translate3d(0px, 0, 0);
}

.swiper-horizontal {
  touch-action: pan-y;
}

.swiper-vertical {
  touch-action: pan-x;
}

.swiper-slide {
  flex-shrink: 0;
  width: 100%;
  height: 100%;
  position: relative;
  transition-property: transform;
  display: block;
}

.swiper-slide-invisible-blank {
  visibility: hidden;
}

/* Auto Height */
.swiper-autoheight,
.swiper-autoheight .swiper-slide {
  height: auto;
}

.swiper-autoheight .swiper-wrapper {
  align-items: flex-start;
  transition-property: transform, height;
}

.swiper-backface-hidden .swiper-slide {
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* 3D Effects */
.swiper-3d.swiper-css-mode .swiper-wrapper {
  perspective: 1200px;
}

.swiper-3d .swiper-wrapper {
  transform-style: preserve-3d;
}

.swiper-3d {
  perspective: 1200px;
}

.swiper-3d .swiper-slide,
.swiper-3d .swiper-cube-shadow {
  transform-style: preserve-3d;
}

/* CSS Mode */
.swiper-css-mode > .swiper-wrapper {
  overflow: auto;
  scrollbar-width: none;
  /* For Firefox */
  -ms-overflow-style: none;
  /* For Internet Explorer and Edge */
}

.swiper-css-mode > .swiper-wrapper::-webkit-scrollbar {
  display: none;
}

.swiper-css-mode > .swiper-wrapper > .swiper-slide {
  scroll-snap-align: start start;
}

.swiper-css-mode.swiper-horizontal > .swiper-wrapper {
  scroll-snap-type: x mandatory;
}

.swiper-css-mode.swiper-vertical > .swiper-wrapper {
  scroll-snap-type: y mandatory;
}

.swiper-css-mode.swiper-free-mode > .swiper-wrapper {
  scroll-snap-type: none;
}

.swiper-css-mode.swiper-free-mode > .swiper-wrapper > .swiper-slide {
  scroll-snap-align: none;
}

.swiper-css-mode.swiper-centered > .swiper-wrapper::before {
  content: "";
  flex-shrink: 0;
  order: 9999;
}

.swiper-css-mode.swiper-centered > .swiper-wrapper > .swiper-slide {
  scroll-snap-align: center center;
  scroll-snap-stop: always;
}

.swiper-css-mode.swiper-centered.swiper-horizontal
  > .swiper-wrapper
  > .swiper-slide:first-child {
  margin-inline-start: var(--swiper-centered-offset-before);
}

.swiper-css-mode.swiper-centered.swiper-horizontal > .swiper-wrapper::before {
  height: 100%;
  min-height: 1px;
  width: var(--swiper-centered-offset-after);
}

.swiper-css-mode.swiper-centered.swiper-vertical
  > .swiper-wrapper
  > .swiper-slide:first-child {
  margin-block-start: var(--swiper-centered-offset-before);
}

.swiper-css-mode.swiper-centered.swiper-vertical > .swiper-wrapper::before {
  width: 100%;
  min-width: 1px;
  height: var(--swiper-centered-offset-after);
}

/* Slide styles start */
/* 3D Shadows */
.swiper-3d .swiper-slide-shadow,
.swiper-3d .swiper-slide-shadow-left,
.swiper-3d .swiper-slide-shadow-right,
.swiper-3d .swiper-slide-shadow-top,
.swiper-3d .swiper-slide-shadow-bottom,
.swiper-3d .swiper-slide-shadow,
.swiper-3d .swiper-slide-shadow-left,
.swiper-3d .swiper-slide-shadow-right,
.swiper-3d .swiper-slide-shadow-top,
.swiper-3d .swiper-slide-shadow-bottom {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;
}

.swiper-3d .swiper-slide-shadow {
  background: rgba(0, 0, 0, 0.15);
}

.swiper-3d .swiper-slide-shadow-left {
  background-image: linear-gradient(
    to left,
    rgba(0, 0, 0, 0.5),
    rgba(0, 0, 0, 0)
  );
}

.swiper-3d .swiper-slide-shadow-right {
  background-image: linear-gradient(
    to right,
    rgba(0, 0, 0, 0.5),
    rgba(0, 0, 0, 0)
  );
}

.swiper-3d .swiper-slide-shadow-top {
  background-image: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.5),
    rgba(0, 0, 0, 0)
  );
}

.swiper-3d .swiper-slide-shadow-bottom {
  background-image: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.5),
    rgba(0, 0, 0, 0)
  );
}

.swiper-lazy-preloader {
  width: 42px;
  height: 42px;
  position: absolute;
  left: 50%;
  top: 50%;
  margin-left: -21px;
  margin-top: -21px;
  z-index: 10;
  transform-origin: 50%;
  box-sizing: border-box;
  border: 4px solid var(--swiper-preloader-color, var(--swiper-theme-color));
  border-radius: 50%;
  border-top-color: transparent;
}

.swiper:not(.swiper-watch-progress) .swiper-lazy-preloader,
.swiper-watch-progress .swiper-slide-visible .swiper-lazy-preloader {
  animation: swiper-preloader-spin 1s infinite linear;
}

.swiper-lazy-preloader-white {
  --swiper-preloader-color: #fff;
}

.swiper-lazy-preloader-black {
  --swiper-preloader-color: #000;
}

@keyframes swiper-preloader-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
/* Slide styles end */
.swiper-virtual .swiper-slide {
  -webkit-backface-visibility: hidden;
  transform: translateZ(0);
}

.swiper-virtual.swiper-css-mode .swiper-wrapper::after {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  pointer-events: none;
}

.swiper-virtual.swiper-css-mode.swiper-horizontal .swiper-wrapper::after {
  height: 1px;
  width: var(--swiper-virtual-size);
}

.swiper-virtual.swiper-css-mode.swiper-vertical .swiper-wrapper::after {
  width: 1px;
  height: var(--swiper-virtual-size);
}

:root {
  --swiper-navigation-size: 44px;
  /*
  --swiper-navigation-top-offset: 50%;
  --swiper-navigation-sides-offset: 10px;
  --swiper-navigation-color: var(--swiper-theme-color);
  */
}

.swiper-button-prev,
.swiper-button-next {
  position: absolute;
  top: var(--swiper-navigation-top-offset, 50%);
  width: calc(var(--swiper-navigation-size) / 44 * 27);
  height: var(--swiper-navigation-size);
  margin-top: calc(0px - var(--swiper-navigation-size) / 2);
  z-index: 10;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--swiper-navigation-color, var(--swiper-theme-color));
}

.swiper-button-prev.swiper-button-disabled,
.swiper-button-next.swiper-button-disabled {
  opacity: 0.35;
  cursor: auto;
  pointer-events: none;
}

.swiper-button-prev.swiper-button-hidden,
.swiper-button-next.swiper-button-hidden {
  opacity: 0;
  cursor: auto;
  pointer-events: none;
}

.swiper-navigation-disabled .swiper-button-prev,
.swiper-navigation-disabled .swiper-button-next {
  display: none !important;
}

.swiper-button-prev svg,
.swiper-button-next svg {
  width: 100%;
  height: 100%;
  -o-object-fit: contain;
  object-fit: contain;
  transform-origin: center;
}

.swiper-rtl .swiper-button-prev svg,
.swiper-rtl .swiper-button-next svg {
  transform: rotate(180deg);
}

.swiper-button-prev,
.swiper-rtl .swiper-button-next {
  left: var(--swiper-navigation-sides-offset, 10px);
  right: auto;
}

.swiper-button-next,
.swiper-rtl .swiper-button-prev {
  right: var(--swiper-navigation-sides-offset, 10px);
  left: auto;
}

.swiper-button-lock {
  display: none;
}

/* Navigation font start */
.swiper-button-prev:after,
.swiper-button-next:after {
  font-family: swiper-icons;
  font-size: var(--swiper-navigation-size);
  text-transform: none !important;
  letter-spacing: 0;
  font-variant: initial;
  line-height: 1;
}

.swiper-button-prev:after,
.swiper-rtl .swiper-button-next:after {
  content: "prev";
}

.swiper-button-next,
.swiper-rtl .swiper-button-prev {
  right: var(--swiper-navigation-sides-offset, 10px);
  left: auto;
}

.swiper-button-next:after,
.swiper-rtl .swiper-button-prev:after {
  content: "next";
}

/* Navigation font end */
:root {
  /*
  --swiper-pagination-color: var(--swiper-theme-color);
  --swiper-pagination-left: auto;
  --swiper-pagination-right: 8px;
  --swiper-pagination-bottom: 8px;
  --swiper-pagination-top: auto;
  --swiper-pagination-fraction-color: inherit;
  --swiper-pagination-progressbar-bg-color: rgba(0,0,0,0.25);
  --swiper-pagination-progressbar-size: 4px;
  --swiper-pagination-bullet-size: 8px;
  --swiper-pagination-bullet-width: 8px;
  --swiper-pagination-bullet-height: 8px;
  --swiper-pagination-bullet-border-radius: 50%;
  --swiper-pagination-bullet-inactive-color: #000;
  --swiper-pagination-bullet-inactive-opacity: 0.2;
  --swiper-pagination-bullet-opacity: 1;
  --swiper-pagination-bullet-horizontal-gap: 4px;
  --swiper-pagination-bullet-vertical-gap: 6px;
  */
}

.swiper-pagination {
  position: absolute;
  text-align: center;
  transition: 300ms opacity;
  transform: translate3d(0, 0, 0);
  z-index: 10;
}

.swiper-pagination.swiper-pagination-hidden {
  opacity: 0;
}

.swiper-pagination-disabled > .swiper-pagination,
.swiper-pagination.swiper-pagination-disabled {
  display: none !important;
}

/* Common Styles */
.swiper-pagination-fraction,
.swiper-pagination-custom,
.swiper-horizontal > .swiper-pagination-bullets,
.swiper-pagination-bullets.swiper-pagination-horizontal {
  bottom: var(--swiper-pagination-bottom, 8px);
  top: var(--swiper-pagination-top, auto);
  left: 0;
  width: 100%;
}

/* Bullets */
.swiper-pagination-bullets-dynamic {
  overflow: hidden;
  font-size: 0;
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet {
  transform: scale(0.33);
  position: relative;
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active {
  transform: scale(1);
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-main {
  transform: scale(1);
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev {
  transform: scale(0.66);
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-prev-prev {
  transform: scale(0.33);
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next {
  transform: scale(0.66);
}

.swiper-pagination-bullets-dynamic .swiper-pagination-bullet-active-next-next {
  transform: scale(0.33);
}

.swiper-pagination-bullet {
  width: var(
    --swiper-pagination-bullet-width,
    var(--swiper-pagination-bullet-size, 8px)
  );
  height: var(
    --swiper-pagination-bullet-height,
    var(--swiper-pagination-bullet-size, 8px)
  );
  display: inline-block;
  border-radius: var(--swiper-pagination-bullet-border-radius, 50%);
  background: var(--swiper-pagination-bullet-inactive-color, #000);
  opacity: var(--swiper-pagination-bullet-inactive-opacity, 0.2);
}

button.swiper-pagination-bullet {
  border: none;
  margin: 0;
  padding: 0;
  box-shadow: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.swiper-pagination-clickable .swiper-pagination-bullet {
  cursor: pointer;
}

.swiper-pagination-bullet:only-child {
  display: none !important;
}

.swiper-pagination-bullet-active {
  opacity: var(--swiper-pagination-bullet-opacity, 1);
  background: var(--swiper-pagination-color, var(--swiper-theme-color));
}

.swiper-vertical > .swiper-pagination-bullets,
.swiper-pagination-vertical.swiper-pagination-bullets {
  right: var(--swiper-pagination-right, 8px);
  left: var(--swiper-pagination-left, auto);
  top: 50%;
  transform: translate3d(0px, -50%, 0);
}

.swiper-vertical > .swiper-pagination-bullets .swiper-pagination-bullet,
.swiper-pagination-vertical.swiper-pagination-bullets
  .swiper-pagination-bullet {
  margin: var(--swiper-pagination-bullet-vertical-gap, 6px) 0;
  display: block;
}

.swiper-vertical > .swiper-pagination-bullets.swiper-pagination-bullets-dynamic,
.swiper-pagination-vertical.swiper-pagination-bullets.swiper-pagination-bullets-dynamic {
  top: 50%;
  transform: translateY(-50%);
  width: 8px;
}

.swiper-vertical
  > .swiper-pagination-bullets.swiper-pagination-bullets-dynamic
  .swiper-pagination-bullet,
.swiper-pagination-vertical.swiper-pagination-bullets.swiper-pagination-bullets-dynamic
  .swiper-pagination-bullet {
  display: inline-block;
  transition: 200ms transform, 200ms top;
}

.swiper-horizontal > .swiper-pagination-bullets .swiper-pagination-bullet,
.swiper-pagination-horizontal.swiper-pagination-bullets
  .swiper-pagination-bullet {
  margin: 0 var(--swiper-pagination-bullet-horizontal-gap, 4px);
}

.swiper-horizontal
  > .swiper-pagination-bullets.swiper-pagination-bullets-dynamic,
.swiper-pagination-horizontal.swiper-pagination-bullets.swiper-pagination-bullets-dynamic {
  left: 50%;
  transform: translateX(-50%);
  white-space: nowrap;
}

.swiper-horizontal
  > .swiper-pagination-bullets.swiper-pagination-bullets-dynamic
  .swiper-pagination-bullet,
.swiper-pagination-horizontal.swiper-pagination-bullets.swiper-pagination-bullets-dynamic
  .swiper-pagination-bullet {
  transition: 200ms transform, 200ms left;
}

.swiper-horizontal.swiper-rtl
  > .swiper-pagination-bullets-dynamic
  .swiper-pagination-bullet {
  transition: 200ms transform, 200ms right;
}

/* Fraction */
.swiper-pagination-fraction {
  color: var(--swiper-pagination-fraction-color, inherit);
}

/* Progress */
.swiper-pagination-progressbar {
  background: var(
    --swiper-pagination-progressbar-bg-color,
    rgba(0, 0, 0, 0.25)
  );
  position: absolute;
}

.swiper-pagination-progressbar .swiper-pagination-progressbar-fill {
  background: var(--swiper-pagination-color, var(--swiper-theme-color));
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  transform: scale(0);
  transform-origin: left top;
}

.swiper-rtl .swiper-pagination-progressbar .swiper-pagination-progressbar-fill {
  transform-origin: right top;
}

.swiper-horizontal > .swiper-pagination-progressbar,
.swiper-pagination-progressbar.swiper-pagination-horizontal,
.swiper-vertical
  > .swiper-pagination-progressbar.swiper-pagination-progressbar-opposite,
.swiper-pagination-progressbar.swiper-pagination-vertical.swiper-pagination-progressbar-opposite {
  width: 100%;
  height: var(--swiper-pagination-progressbar-size, 4px);
  left: 0;
  top: 0;
}

.swiper-vertical > .swiper-pagination-progressbar,
.swiper-pagination-progressbar.swiper-pagination-vertical,
.swiper-horizontal
  > .swiper-pagination-progressbar.swiper-pagination-progressbar-opposite,
.swiper-pagination-progressbar.swiper-pagination-horizontal.swiper-pagination-progressbar-opposite {
  width: var(--swiper-pagination-progressbar-size, 4px);
  height: 100%;
  left: 0;
  top: 0;
}

.swiper-pagination-lock {
  display: none;
}

:root {
  /*
  --swiper-scrollbar-border-radius: 10px;
  --swiper-scrollbar-top: auto;
  --swiper-scrollbar-bottom: 4px;
  --swiper-scrollbar-left: auto;
  --swiper-scrollbar-right: 4px;
  --swiper-scrollbar-sides-offset: 1%;
  --swiper-scrollbar-bg-color: rgba(0, 0, 0, 0.1);
  --swiper-scrollbar-drag-bg-color: rgba(0, 0, 0, 0.5);
  --swiper-scrollbar-size: 4px;
  */
}

.swiper-scrollbar {
  border-radius: var(--swiper-scrollbar-border-radius, 10px);
  position: relative;
  touch-action: none;
  background: var(--swiper-scrollbar-bg-color, rgba(0, 0, 0, 0.1));
}

.swiper-scrollbar-disabled > .swiper-scrollbar,
.swiper-scrollbar.swiper-scrollbar-disabled {
  display: none !important;
}

.swiper-horizontal > .swiper-scrollbar,
.swiper-scrollbar.swiper-scrollbar-horizontal {
  position: absolute;
  left: var(--swiper-scrollbar-sides-offset, 1%);
  bottom: var(--swiper-scrollbar-bottom, 4px);
  top: var(--swiper-scrollbar-top, auto);
  z-index: 50;
  height: var(--swiper-scrollbar-size, 4px);
  width: calc(100% - 2 * var(--swiper-scrollbar-sides-offset, 1%));
}

.swiper-vertical > .swiper-scrollbar,
.swiper-scrollbar.swiper-scrollbar-vertical {
  position: absolute;
  left: var(--swiper-scrollbar-left, auto);
  right: var(--swiper-scrollbar-right, 4px);
  top: var(--swiper-scrollbar-sides-offset, 1%);
  z-index: 50;
  width: var(--swiper-scrollbar-size, 4px);
  height: calc(100% - 2 * var(--swiper-scrollbar-sides-offset, 1%));
}

.swiper-scrollbar-drag {
  height: 100%;
  width: 100%;
  position: relative;
  background: var(--swiper-scrollbar-drag-bg-color, rgba(0, 0, 0, 0.5));
  border-radius: var(--swiper-scrollbar-border-radius, 10px);
  left: 0;
  top: 0;
}

.swiper-scrollbar-cursor-drag {
  cursor: move;
}

.swiper-scrollbar-lock {
  display: none;
}

/* Zoom container styles start */
.swiper-zoom-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.swiper-zoom-container > img,
.swiper-zoom-container > svg,
.swiper-zoom-container > canvas {
  max-width: 100%;
  max-height: 100%;
  -o-object-fit: contain;
  object-fit: contain;
}

/* Zoom container styles end */
.swiper-slide-zoomed {
  cursor: move;
  touch-action: none;
}

/* a11y */
.swiper .swiper-notification {
  position: absolute;
  left: 0;
  top: 0;
  pointer-events: none;
  opacity: 0;
  z-index: -1000;
}

.swiper-free-mode > .swiper-wrapper {
  transition-timing-function: ease-out;
  margin: 0 auto;
}

.swiper-grid > .swiper-wrapper {
  flex-wrap: wrap;
}

.swiper-grid-column > .swiper-wrapper {
  flex-wrap: wrap;
  flex-direction: column;
}

.swiper-fade.swiper-free-mode .swiper-slide {
  transition-timing-function: ease-out;
}

.swiper-fade .swiper-slide {
  pointer-events: none;
  transition-property: opacity;
}

.swiper-fade .swiper-slide .swiper-slide {
  pointer-events: none;
}

.swiper-fade .swiper-slide-active {
  pointer-events: auto;
}

.swiper-fade .swiper-slide-active .swiper-slide-active {
  pointer-events: auto;
}

.swiper.swiper-cube {
  overflow: visible;
}

.swiper-cube .swiper-slide {
  pointer-events: none;
  backface-visibility: hidden;
  z-index: 1;
  visibility: hidden;
  transform-origin: 0 0;
  width: 100%;
  height: 100%;
}

.swiper-cube .swiper-slide .swiper-slide {
  pointer-events: none;
}

.swiper-cube.swiper-rtl .swiper-slide {
  transform-origin: 100% 0;
}

.swiper-cube .swiper-slide-active,
.swiper-cube .swiper-slide-active .swiper-slide-active {
  pointer-events: auto;
}

.swiper-cube .swiper-slide-active,
.swiper-cube .swiper-slide-next,
.swiper-cube .swiper-slide-prev {
  pointer-events: auto;
  visibility: visible;
}

.swiper-cube .swiper-cube-shadow {
  position: absolute;
  left: 0;
  bottom: 0px;
  width: 100%;
  height: 100%;
  opacity: 0.6;
  z-index: 0;
}

.swiper-cube .swiper-cube-shadow:before {
  content: "";
  background: #000;
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  right: 0;
  filter: blur(50px);
}

.swiper-cube .swiper-slide-next + .swiper-slide {
  pointer-events: auto;
  visibility: visible;
}

/* Cube slide shadows start */
.swiper-cube .swiper-slide-shadow-cube.swiper-slide-shadow-top,
.swiper-cube .swiper-slide-shadow-cube.swiper-slide-shadow-bottom,
.swiper-cube .swiper-slide-shadow-cube.swiper-slide-shadow-left,
.swiper-cube .swiper-slide-shadow-cube.swiper-slide-shadow-right {
  z-index: 0;
  backface-visibility: hidden;
}

/* Cube slide shadows end */
.swiper.swiper-flip {
  overflow: visible;
}

.swiper-flip .swiper-slide {
  pointer-events: none;
  backface-visibility: hidden;
  z-index: 1;
}

.swiper-flip .swiper-slide .swiper-slide {
  pointer-events: none;
}

.swiper-flip .swiper-slide-active,
.swiper-flip .swiper-slide-active .swiper-slide-active {
  pointer-events: auto;
}

/* Flip slide shadows start */
.swiper-flip .swiper-slide-shadow-flip.swiper-slide-shadow-top,
.swiper-flip .swiper-slide-shadow-flip.swiper-slide-shadow-bottom,
.swiper-flip .swiper-slide-shadow-flip.swiper-slide-shadow-left,
.swiper-flip .swiper-slide-shadow-flip.swiper-slide-shadow-right {
  z-index: 0;
  backface-visibility: hidden;
}

/* Flip slide shadows end */
.swiper-creative .swiper-slide {
  backface-visibility: hidden;
  overflow: hidden;
  transition-property: transform, opacity, height;
}

.swiper.swiper-cards {
  overflow: visible;
}

.swiper-cards .swiper-slide {
  transform-origin: center bottom;
  backface-visibility: hidden;
  overflow: hidden;
}

.c-loader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  z-index: 850;
  pointer-events: none;
}
.c-loader:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 5;
  background-color: #1d1d1d;
  opacity: 0;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
html:not(.is-loaded) .c-loader {
  pointer-events: all;
}
html:not(.is-loaded) .c-loader:before {
  opacity: 0.8;
}

.c-loader_col {
  position: relative;
  z-index: 10;
  background-color: #000000;
  height: 100%;
  transform: scale3d(1, 0, 1);
  transition: transform 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
  transform-origin: center bottom;
}
html:not(.is-loaded) .c-loader_col {
  transform: scale3d(1, 1, 1);
  transform-origin: center top;
}
@media (min-width: 1000px) {
  .c-loader_col {
    width: calc((100vw - 13.125rem) * 4 / 20 + 2.5rem + 2px);
    margin-left: -1px;
  }
  .c-loader_col:nth-child(3) {
    width: calc((100vw - 13.125rem) * 6 / 20 + 3.75rem + 2px);
  }
  .c-loader_col:nth-child(4) {
    width: calc((100vw - 13.125rem) * 3 / 20 + 1.875rem + 2px);
  }
  .c-loader_col:nth-child(5) {
    width: calc((100vw - 13.125rem) * 3 / 20 + 2.5rem + 2px);
  }
  .c-loader_col:nth-child(5) {
    transition-delay: 0.075s;
  }
  .c-loader_col:nth-child(4) {
    transition-delay: 0.12s;
  }
  .c-loader_col:nth-child(3) {
    transition-delay: 0.165s;
  }
  .c-loader_col:nth-child(2) {
    transition-delay: 0.21s;
  }
  .c-loader_col:nth-child(1) {
    transition-delay: 0.255s;
  }
}
@media (max-width: 999px) {
  .c-loader_col {
    width: 33.3333333333%;
    width: 33.3333333333%;
    width: 33.3333333333%;
  }
  .c-loader_col:nth-child(2) {
    transition-delay: 0.075s;
  }
  .c-loader_col:nth-child(1) {
    transition-delay: 0.12s;
  }
  .c-loader_col:nth-child(0) {
    transition-delay: 0.165s;
  }
}

.c-loader_spinner {
  position: absolute;
  bottom: 0.625rem;
  right: 0.625rem;
  width: 3.125rem;
  height: 1.7578125rem;
  display: flex;
  transform: scaleY(0);
  transform-origin: center bottom;
  transition: transform 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
  z-index: 20;
}
html:not(.is-loaded) .c-loader_spinner {
  transform: scaleY(1);
  transition-delay: 0.3s;
}
.c-loader_spinner div {
  flex-grow: 1;
  background-color: #ffffff;
  animation: loaderSpinnerCol 1s cubic-bezier(0.215, 0.61, 0.355, 1) infinite;
  margin-left: -1px;
}
.c-loader_spinner div:nth-child(1) {
  animation-delay: -0.1s;
}
.c-loader_spinner div:nth-child(2) {
  animation-delay: -0.2s;
}
.c-loader_spinner div:nth-child(3) {
  animation-delay: -0.3s;
}
.c-loader_spinner div:nth-child(4) {
  animation-delay: -0.4s;
}

@keyframes loaderSpinnerCol {
  0% {
    transform: scale3d(1, 0.25, 1);
    transform-origin: bottom center;
  }
  50% {
    transform: scale3d(1, 1, 1);
    transform-origin: bottom center;
  }
  100% {
    transform: scale3d(1, 0.25, 1);
    transform-origin: bottom center;
  }
}
.c-scrollbar {
  position: absolute;
  right: 0;
  top: 0;
  width: 11px;
  height: 100vh;
  transform-origin: center right;
  transition: transform 0.3s, opacity 0.3s;
  opacity: 0;
}
.c-scrollbar:hover {
  transform: scaleX(1.45);
}
.c-scrollbar:hover,
.has-scroll-scrolling .c-scrollbar,
.has-scroll-dragging .c-scrollbar {
  opacity: 1;
}

.c-scrollbar_thumb {
  position: absolute;
  top: 0;
  right: 0;
  background-color: black;
  opacity: 0.5;
  width: 7px;
  border-radius: 10px;
  margin: 2px;
  cursor: grab;
}
.has-scroll-dragging .c-scrollbar_thumb {
  cursor: grabbing;
}
html[data-template="case-study-list"] .c-scrollbar_thumb,
html[data-template="case-study-details"] .c-scrollbar_thumb {
  background-color: #d3fd50;
  opacity: 1;
}

.c-heading,
.c-project-summary_punchline {
  line-height: 1.5;
  margin-bottom: 1.875rem;
}
.c-heading.-h1,
.c-project-summary_punchline {
  font-size: 8.75rem;
  text-transform: uppercase;
  line-height: 0.8857142857;
}
@media (max-width: 1399px) {
  .c-heading.-h1,
  .c-project-summary_punchline {
    font-size: 7.5rem;
  }
}
@media (max-width: 1199px) {
  .c-heading.-h1,
  .c-project-summary_punchline {
    font-size: 6.25rem;
  }
}
@media (max-width: 699px) {
  .c-heading.-h1,
  .c-project-summary_punchline {
    font-size: 4.5rem;
  }
}
.c-heading.-h1-big,
.-h1-big.c-project-summary_punchline {
  font-size: 12.5rem;
  text-transform: uppercase;
  line-height: 0.885;
}
.c-heading.-h1-big sup,
.-h1-big.c-project-summary_punchline sup {
  font-size: 20%;
  vertical-align: baseline;
  top: initial;
  bottom: 2.88em;
}
.c-heading.-h1-big.-no-margin,
.-h1-big.-no-margin.c-project-summary_punchline {
  margin-bottom: -0.1em;
}
@media (max-width: 1199px) {
  .c-heading.-h1-big,
  .-h1-big.c-project-summary_punchline {
    font-size: 9.375rem;
  }
}
@media (max-width: 699px) {
  .c-heading.-h1-big,
  .-h1-big.c-project-summary_punchline {
    font-size: 5.625rem;
  }
}
.c-heading.-h2,
.-h2.c-project-summary_punchline {
  font-size: 1.75rem;
}
.c-heading.-h3,
.-h3.c-project-summary_punchline {
  font-size: 1.5rem;
}
.c-heading.-h4,
.-h4.c-project-summary_punchline {
  font-size: 1.25rem;
}
.c-heading.-h5,
.-h5.c-project-summary_punchline {
  font-size: 1.125rem;
}
.c-heading.-h6,
.-h6.c-project-summary_punchline {
  font-size: 1rem;
}

.c-button {
  display: inline-block;
  text-transform: uppercase;
  color: inherit;
  line-height: 0.7;
  border: 2px solid;
  border-radius: 999em;
  padding: 0.22em 0.3em 0 0.3em;
  font-size: 2.1875rem;
}
.c-button.-thicker {
  border-width: 3px;
}
.c-button:hover,
.c-button:focus,
.c-button_wrapper:hover .c-button,
.c-button_wrapper:focus .c-button {
  color: #d3fd50;
}
.c-button:hover.-dark-hover,
.c-button:focus.-dark-hover,
.c-button_wrapper:hover .c-button.-dark-hover,
.c-button_wrapper:focus .c-button.-dark-hover {
  color: #ffffff;
  background-color: #000000;
  border-color: #000000;
}
.c-button svg {
  display: inline-block;
  margin-top: -0.2em;
  width: 1em;
  height: 0.714em;
}

.c-form_item {
  position: relative;
  margin-bottom: 1.875rem;
}

.c-form_label,
.c-form_checkboxLabel,
.c-form_radioLabel {
  display: block;
  margin-bottom: 0.625rem;
}

.c-form_input,
.c-form_textarea,
.c-form_select_input {
  padding: 0.625rem;
  border: 1px solid lightgray;
  background-color: white;
}
.c-form_input:hover,
.c-form_textarea:hover,
.c-form_select_input:hover {
  border-color: darkgray;
}
.c-form_input:focus,
.c-form_textarea:focus,
.c-form_select_input:focus {
  border-color: dimgray;
}
.c-form_input::-moz-placeholder,
.c-form_textarea::-moz-placeholder,
.c-form_select_input::-moz-placeholder {
  color: gray;
}
.c-form_input::placeholder,
.c-form_textarea::placeholder,
.c-form_select_input::placeholder {
  color: gray;
}

.c-form_checkboxLabel,
.c-form_radioLabel {
  position: relative;
  display: inline-block;
  margin-right: 0.625rem;
  margin-bottom: 0;
  padding-left: 1.75rem;
  cursor: pointer;
}
.c-form_checkboxLabel::before,
.c-form_radioLabel::before,
.c-form_checkboxLabel::after,
.c-form_radioLabel::after {
  position: absolute;
  top: 50%;
  left: 0;
  display: inline-block;
  margin-top: -0.5625rem;
  padding: 0;
  width: 1.125rem;
  height: 1.125rem;
  content: "";
}
.c-form_checkboxLabel::before,
.c-form_radioLabel::before {
  background-color: #ffffff;
  border: 1px solid lightgray;
}
.c-form_checkboxLabel::after,
.c-form_radioLabel::after {
  border-color: transparent;
  background-color: transparent;
  background-image: url("data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20version%3D%221.1%22%20x%3D%220%22%20y%3D%220%22%20width%3D%2213%22%20height%3D%2210.5%22%20viewBox%3D%220%200%2013%2010.5%22%20enable-background%3D%22new%200%200%2013%2010.5%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpath%20fill%3D%22%23424242%22%20d%3D%22M4.8%205.8L2.4%203.3%200%205.7l4.8%204.8L13%202.4c0%200-2.4-2.4-2.4-2.4L4.8%205.8z%22%2F%3E%3C%2Fsvg%3E");
  background-position: center;
  background-size: 0.75rem;
  background-repeat: no-repeat;
  opacity: 0;
}
.c-form_checkboxLabel:hover::before,
.c-form_radioLabel:hover::before {
  border-color: darkgray;
}
.c-form_checkbox:focus + .c-form_checkboxLabel::before,
.c-form_radio:focus + .c-form_checkboxLabel::before,
.c-form_checkbox:focus + .c-form_radioLabel::before,
.c-form_radio:focus + .c-form_radioLabel::before {
  border-color: dimgray;
}
.c-form_checkbox:checked + .c-form_checkboxLabel::after,
.c-form_radio:checked + .c-form_checkboxLabel::after,
.c-form_checkbox:checked + .c-form_radioLabel::after,
.c-form_radio:checked + .c-form_radioLabel::after {
  opacity: 1;
}

.c-form_checkbox,
.c-form_radio {
  position: absolute;
  width: 0;
  opacity: 0;
}

.c-form_radioLabel::before,
.c-form_radioLabel::after {
  border-radius: 50%;
}
.c-form_radioLabel::after {
  background-image: url("data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20version%3D%221.1%22%20x%3D%220%22%20y%3D%220%22%20width%3D%2213%22%20height%3D%2213%22%20viewBox%3D%220%200%2013%2013%22%20enable-background%3D%22new%200%200%2013%2013%22%20xml%3Aspace%3D%22preserve%22%3E%3Ccircle%20fill%3D%22%23424242%22%20cx%3D%226.5%22%20cy%3D%226.5%22%20r%3D%226.5%22%2F%3E%3C%2Fsvg%3E");
  background-size: 0.375rem;
}

.c-form_select {
  position: relative;
  cursor: pointer;
}
.c-form_select::after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 2;
  width: 2.5rem;
  background-image: url("data:image/svg+xml,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20version%3D%221.1%22%20x%3D%220%22%20y%3D%220%22%20width%3D%2213%22%20height%3D%2211.3%22%20viewBox%3D%220%200%2013%2011.3%22%20enable-background%3D%22new%200%200%2013%2011.3%22%20xml%3Aspace%3D%22preserve%22%3E%3Cpolygon%20fill%3D%22%23424242%22%20points%3D%226.5%2011.3%203.3%205.6%200%200%206.5%200%2013%200%209.8%205.6%20%22%2F%3E%3C%2Fsvg%3E");
  background-position: center;
  background-size: 0.5rem;
  background-repeat: no-repeat;
  content: "";
  pointer-events: none;
}

.c-form_select_input {
  position: relative;
  z-index: 1;
  padding-right: 2.5rem;
  cursor: pointer;
}

.c-form_textarea {
  min-height: 12.5rem;
}

.c-section.-margin {
  margin-top: 15rem;
  margin-bottom: 15rem;
}
@media (max-width: 699px) {
  .c-section.-margin {
    margin-top: 5rem;
    margin-bottom: 5rem;
  }
}
.c-section.-margin-bottom {
  margin-bottom: 15rem;
}
@media (max-width: 699px) {
  .c-section.-margin-bottom {
    margin-bottom: 5rem;
  }
}

.c-clock {
  display: flex;
  align-items: center;
}
@media (min-width: 1400px) {
  .c-clock {
    font-size: 1.25rem;
  }
}
.c-clock svg {
  width: 1.5em;
  height: 1.5em;
  margin-right: 1em;
}
.c-clock > span {
  margin-bottom: -0.2em;
}
.c-home .c-clock {
  position: absolute;
  bottom: 0.625rem;
  left: 0.625rem;
}
@media (max-width: 999px) {
  .c-home .c-clock {
    display: none;
  }
}
@media (max-width: 999px) {
  .c-menu .c-clock {
    display: none;
  }
}

.c-header_link {
  color: #ffffff;
}
.c-header_link span {
  text-transform: uppercase;
  font-size: 1.25rem;
  line-height: 0.9;
  opacity: 0;
  color: currentColor;
  z-index: 2;
}
.c-header_link:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #000000;
  z-index: 0;
}
.c-header_link:after {
  content: "";
  background-color: #d3fd50;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 101%;
  transform: translate3d(0, -102%, 0);
  transition: transform 0.15s cubic-bezier(0.215, 0.61, 0.355, 1);
  z-index: 1;
}
@media (min-width: 1000px), (min-aspect-ratio: 1/1) {
  html.is-ready[data-has-quicknav="true"]:not(.hide-quicknav)
    .c-header_link
    span {
    opacity: 1;
    transition: opacity 0.3s cubic-bezier(0.215, 0.61, 0.355, 1) 0.3s;
  }
}
.c-header_link:hover,
.c-header_link:focus {
  color: #000000;
}
.c-header_link:hover:after,
.c-header_link:focus:after {
  transform: translate3d(0, 0, 0);
}

.c-header_logo {
  position: fixed;
  top: 0;
  left: 0;
  pointer-events: all;
  display: inline-block;
  padding: 0.625rem;
  color: inherit;
  z-index: 900;
  color: #000000;
  transition: color 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
}
html[data-theme="dark"] .c-header_logo,
html.has-menu-opened .c-header_logo,
html.is-loading .c-header_logo {
  color: #ffffff;
}
.c-header_logo svg {
  fill: currentColor;
  display: inline-block;
  width: 7.3125rem;
  height: 3.125rem;
  transform-origin: top left;
  transition: transform 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
}
html.has-preview-bar-active .c-header_logo svg {
  transform: scale3d(0.6, 0.6, 1);
}

.c-header_quicknav {
  position: fixed;
  top: 0;
  display: flex;
  z-index: 700;
  pointer-events: none;
}
@media (min-width: 1000px) {
  .c-header_quicknav {
    right: calc((100vw - 13.125rem) * 3 / 20 + 2.5rem);
  }
}
@media (max-width: 999px) {
  .c-header_quicknav {
    right: 33.3333333333%;
  }
}
@media (max-width: 699px) {
  .c-header_quicknav {
    right: 50%;
  }
}
@media (max-width: 999px), (max-aspect-ratio: 1/1) {
  .c-header_quicknav {
    display: none;
  }
}

.c-header_quicknav_item {
  position: relative;
  display: flex;
  transform: translate3d(0, -100%, 0);
  transition-property: transform;
  transition-duration: 0.3s;
  transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
}
.c-header_quicknav_item:nth-child(1) {
  height: 3.4375rem;
  width: calc((100vw - 13.125rem) * 4 / 20 + 2.5rem);
}
.c-header_quicknav_item:nth-child(2) {
  height: 5.625rem;
  width: calc((100vw - 13.125rem) * 6 / 20 + 3.75rem);
}
html.is-ready[data-has-quicknav="true"]:not(.hide-quicknav)
  .c-header_quicknav_item {
  transform: translate3d(0, 0, 0);
  transition-duration: 0.6s;
}
html.is-ready[data-has-quicknav="true"]:not(.hide-quicknav)
  .c-header_quicknav_item:nth-child(1) {
  transition-delay: 0.3s;
}
html.is-ready[data-has-quicknav="true"]:not(.hide-quicknav)
  .c-header_quicknav_item:nth-child(2) {
  transition-delay: 0.15s;
}

.c-header_quicknav_link {
  display: flex;
  width: 100%;
  padding: 0.3125rem 0.625rem;
  align-items: flex-end;
}
html.is-ready[data-has-quicknav="true"]:not(.hide-quicknav)
  .c-header_quicknav_link {
  pointer-events: all;
}

.c-header_menu-btn {
  position: fixed;
  top: 0;
  right: 0;
  color: #ffffff;
  z-index: 700;
  height: 3.125rem;
  display: flex;
  padding-right: 1.5625rem;
  justify-content: flex-end;
  align-items: center;
  transition: color 0.15s cubic-bezier(0.215, 0.61, 0.355, 1);
}
@media (min-width: 1000px) {
  .c-header_menu-btn {
    width: calc((100vw - 13.125rem) * 3 / 20 + 2.5rem);
  }
}
@media (max-width: 999px) {
  .c-header_menu-btn {
    width: 33.3333333333%;
  }
}
@media (max-width: 699px) {
  .c-header_menu-btn {
    width: 50%;
  }
}
.c-header_menu-btn svg {
  position: absolute;
  top: 1.5625rem;
  margin-top: -0.25rem;
  width: 3.8125rem;
  height: 0.5rem;
  z-index: 2;
  stroke: currentColor;
}
.c-header_menu-btn:before {
  height: 3.125rem;
  transition: transform 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
  transform-origin: top center;
}
.c-header_menu-btn span {
  position: absolute;
  top: 2.8125rem;
  left: 0.625rem;
  transform: translate3d(0, -100%, 0);
}
@media (min-width: 1000px), (min-aspect-ratio: 1/1) {
  html.is-ready[data-has-quicknav="true"]:not(.hide-quicknav)
    .c-header_menu-btn {
    height: 8.125rem;
  }
  html.is-ready[data-has-quicknav="true"]:not(.hide-quicknav)
    .c-header_menu-btn
    span {
    transform: translate3d(0, calc(5rem - 100%), 0);
    opacity: 1;
    transition: opacity 0.3s cubic-bezier(0.215, 0.61, 0.355, 1) 0.3s,
      transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  html.is-ready[data-has-quicknav="true"]:not(.hide-quicknav)
    .c-header_menu-btn:before {
    transition: transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
    transform: scale3d(1, 2.6, 1);
  }
}

.c-menu {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 800;
  display: block;
  color: #ffffff;
}
.has-menu-opened .c-menu {
  pointer-events: all;
}

.c-menu_bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  z-index: 5;
}
.c-menu_bg:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 5;
  background-color: #1d1d1d;
  opacity: 0;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.has-menu-opened .c-menu_bg:before {
  opacity: 0.7;
}

.c-menu_bg_col {
  position: relative;
  z-index: 10;
  background-color: #000000;
  height: 100%;
  transform: translate3d(0, -100%, 0);
  transition: transform 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.has-menu-opened .c-menu_bg_col {
  transform: translate3d(0, 0, 0);
}
@media (min-width: 1000px) {
  .c-menu_bg_col {
    width: calc((100vw - 13.125rem) * 4 / 20 + 2.5rem);
  }
  .c-menu_bg_col:nth-child(3) {
    width: calc((100vw - 13.125rem) * 6 / 20 + 3.75rem);
  }
  .c-menu_bg_col:nth-child(4) {
    width: calc((100vw - 13.125rem) * 3 / 20 + 1.875rem);
  }
  .c-menu_bg_col:nth-child(5) {
    width: calc((100vw - 13.125rem) * 3 / 20 + 2.5rem);
  }
  .c-menu_bg_col:nth-child(5) {
    transition-delay: 0.075s;
  }
  .c-menu_bg_col:nth-child(4) {
    transition-delay: 0.12s;
  }
  .c-menu_bg_col:nth-child(3) {
    transition-delay: 0.165s;
  }
  .c-menu_bg_col:nth-child(2) {
    transition-delay: 0.21s;
  }
  .c-menu_bg_col:nth-child(1) {
    transition-delay: 0.255s;
  }
}
@media (max-width: 999px) and (min-width: 700px) {
  .c-menu_bg_col {
    width: 33.3333333333%;
  }
  .c-menu_bg_col:nth-child(4) {
    display: none;
  }
  .c-menu_bg_col:nth-child(5) {
    display: none;
  }
  .c-menu_bg_col:nth-child(4) {
    transition-delay: 0.075s;
  }
  .c-menu_bg_col:nth-child(3) {
    transition-delay: 0.12s;
  }
  .c-menu_bg_col:nth-child(2) {
    transition-delay: 0.165s;
  }
  .c-menu_bg_col:nth-child(1) {
    transition-delay: 0.21s;
  }
}
@media (max-width: 699px) {
  .c-menu_bg_col {
    width: 50%;
  }
  .c-menu_bg_col:nth-child(3) {
    display: none;
  }
  .c-menu_bg_col:nth-child(4) {
    display: none;
  }
  .c-menu_bg_col:nth-child(5) {
    display: none;
  }
  .c-menu_bg_col:nth-child(2) {
    transition-delay: 0.075s;
  }
  .c-menu_bg_col:nth-child(1) {
    transition-delay: 0.12s;
  }
}

.c-menu_inner {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.c-menu_close {
  position: absolute;
  top: 0;
  right: 0;
  width: 8.125rem;
  height: 8.125rem;
  padding: 0.625rem;
  transform: translate3d(100%, 0, 0);
  transition: transform 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.has-menu-opened .c-menu_close {
  transform: translate3d(0, 0, 0);
}
.c-menu_close svg {
  position: relative;
  width: 100%;
  height: 100%;
  stroke: #ffffff;
  z-index: 5;
  transition: stroke 0.15s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.c-menu_close:focus svg,
.c-menu_close:hover svg {
  stroke: #d3fd50;
}

.c-menu_langswitcher {
  position: absolute;
  top: 0.625rem;
  left: 10.3125rem;
  color: rgba(255, 255, 255, 0.3);
  margin-top: -0.2em;
  opacity: 0;
  transition: opacity 0.15s cubic-bezier(0.215, 0.61, 0.355, 1);
  text-transform: uppercase;
}
.has-menu-opened .c-menu_langswitcher {
  opacity: 1;
  transition: opacity 0.3s cubic-bezier(0.215, 0.61, 0.355, 1) 0.3s;
}

.c-menu_langswitcher_item.-active {
  color: #ffffff;
}
.c-menu_langswitcher_item.-link:focus,
.c-menu_langswitcher_item.-link:hover {
  color: #d3fd50;
}

.c-menu_main-nav {
  display: flex;
  flex-direction: column;
  align-items: stretch;
}

.c-menu_main-nav_item {
  border-top: 1px solid rgba(255, 255, 255, 0.5);
  transform: perspective(80vw) rotateX(90deg);
  transform-origin: 50% -10%;
  transition: transform 0.3s cubic-bezier(0.215, 0.61, 0.355, 1) 0s;
  background-color: #000000;
  opacity: 0;
}
.c-menu_main-nav_item:last-child {
  border-bottom: 1px solid rgba(255, 255, 255, 0.5);
}
.has-menu-opened .c-menu_main-nav_item {
  opacity: 1;
  background-color: transparent;
  transform: perspective(80vw) rotateX(0deg);
  transition: transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    background-color 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.has-menu-opened .c-menu_main-nav_item:nth-child(1) {
  transition-delay: 0.3s;
}
.has-menu-opened .c-menu_main-nav_item:nth-child(2) {
  transition-delay: 0.36s;
}
.has-menu-opened .c-menu_main-nav_item:nth-child(3) {
  transition-delay: 0.42s;
}
.has-menu-opened .c-menu_main-nav_item:nth-child(4) {
  transition-delay: 0.48s;
}

.c-menu_main-nav_link {
  position: relative;
  overflow: hidden;
  display: block;
  font-size: 8vw;
  line-height: 0.75;
  padding-top: 0.2em;
  text-transform: uppercase;
  margin-top: -1px;
  margin-bottom: -1px;
}
.c-menu_main-nav_link > span {
  display: block;
  width: 100%;
  text-align: center;
}
@media (max-width: 999px) {
  .c-menu_main-nav_link {
    font-size: 11vw;
  }
}
@media (max-width: 699px) {
  .c-menu_main-nav_link {
    font-size: 15vw;
  }
}

.c-menu_main-nav_link_overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  visibility: hidden;
}

.c-menu_main-nav_link_overlay_inner {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #d3fd50;
  color: #000000;
  white-space: nowrap;
  display: flex;
  padding-top: 0.2em;
  border-top: 1px solid #000000;
}
.c-menu_main-nav_link_overlay_inner > div {
  animation: mainNavLoop 10s linear infinite;
  flex-shrink: 0;
  opacity: 0;
  transition: opacity 0.15s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.c-menu_main-nav_link:hover .c-menu_main-nav_link_overlay_inner > div,
.c-menu_main-nav_link:focus .c-menu_main-nav_link_overlay_inner > div {
  opacity: 1;
  transition: opacity 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.c-menu_main-nav_item:nth-child(1) .c-menu_main-nav_link_overlay_inner > div {
  animation-delay: -2s;
}
.c-menu_main-nav_item:nth-child(2) .c-menu_main-nav_link_overlay_inner > div {
  animation-delay: -4s;
}
.c-menu_main-nav_item:nth-child(3) .c-menu_main-nav_link_overlay_inner > div {
  animation-delay: -6s;
}
.c-menu_main-nav_item:nth-child(4) .c-menu_main-nav_link_overlay_inner > div {
  animation-delay: -8s;
}
.c-menu_main-nav_link_overlay_inner .c-pill-image {
  margin-top: -0.2em;
}
.c-menu_main-nav_link_overlay_inner svg {
  display: inline-block;
  margin-top: -0.2em;
  width: 1em;
  height: 0.714em;
}

@keyframes mainNavLoop {
  0% {
    transform: translate3d(0, 0, 0);
  }
  100% {
    transform: translate3d(-100%, 0, 0);
  }
}
.c-menu_footer {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 0.625rem;
  opacity: 0;
  transition: opacity 0.3s cubic-bezier(0.215, 0.61, 0.355, 1) 0s;
}
.has-menu-opened .c-menu_footer {
  opacity: 1;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1) 0.45s;
}
@media (max-width: 999px) {
  .c-menu_footer {
    display: flex;
    justify-content: space-between;
  }
}
@media (min-width: 700px) and (max-width: 999px) {
  .c-menu_footer {
    align-items: flex-end;
  }
}
@media (max-width: 699px) {
  .c-menu_footer {
    flex-direction: column;
    align-items: center;
  }
}
@media (min-width: 1000px) {
  .c-menu_footer {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 0.625rem;
    align-items: center;
  }
}

@media (max-width: 999px) {
  .c-menu_footer_clock {
    display: none;
  }
}

.c-menu_footer_legals {
  display: flex;
}
@media (max-width: 699px) {
  .c-menu_footer_legals {
    text-align: center;
  }
}
@media (max-width: 999px) {
  .c-menu_footer_legals {
    flex-direction: column;
  }
}
@media (min-width: 1000px) {
  .c-menu_footer_legals {
    justify-content: center;
    gap: 0.625rem;
  }
}
.c-menu_footer_legals li {
  display: flex;
}

.c-menu_footer_legals_link {
  display: inline-block;
  text-transform: uppercase;
  line-height: 1.3;
}
.c-menu_footer_legals_link:focus,
.c-menu_footer_legals_link:hover {
  color: #d3fd50;
}

@media (max-width: 699px) {
  .c-menu_footer_socials {
    margin-top: 0.625rem;
  }
}
@media (min-width: 700px) {
  .c-menu_footer_socials {
    display: flex;
    justify-content: flex-end;
  }
}
@media (min-width: 1000px) {
  .c-menu_footer_socials {
    grid-column: 3/4;
  }
}

.c-home {
  position: relative;
  min-height: 100vh;
  color: #ffffff;
}

.c-home_background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  transform: scale3d(1.5, 1.5, 1);
  transition: transform 0.9s cubic-bezier(0.165, 0.84, 0.44, 1);
  background-size: cover;
  background-position: center center;
}
html.is-loaded .c-home_background {
  transform: scale3d(1, 1, 1);
}
.c-home_background video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
}

.c-home_content {
  display: flex;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 20;
  flex-direction: column;
  justify-content: flex-end;
  min-height: 100%;
  padding: 0.625rem;
}

.c-home_punchline {
  width: 100%;
  font-size: 9.5vw;
  text-transform: uppercase;
  font-weight: 300;
  line-height: 0.8666666667;
  text-align: center;
  padding: 0 10vw;
  flex-grow: 1;
  padding-top: 0.05px;
  padding-bottom: 0.05px;
}
.c-home_punchline::before {
  content: "";
  margin-top: 0.023em;
  display: block;
  height: 0;
}
.c-home_punchline mark {
  display: inline-block;
  position: relative;
  background-color: transparent;
  color: currentColor;
}
.c-home_punchline mark .c-circle {
  position: absolute;
  top: -0.1em;
  left: -0.1em;
  bottom: 0.05em;
  right: -0.1em;
  z-index: 3;
  width: 100%;
  height: 100%;
}
@media (max-width: 999px) {
  .c-home_punchline {
    font-size: 12vw;
    padding-left: 0;
    padding-right: 0;
    position: relative;
    flex-grow: 0;
    margin-bottom: 2.5rem;
  }
}

.c-home_monitor {
  position: relative;
  z-index: 10;
  width: 1.66em;
  height: 0.73em;
  display: inline-block;
  margin-left: 0;
  margin-right: 0;
  margin-top: -0.125em;
  margin-bottom: 0;
  transform: translate3d(0, 0, 0);
  opacity: 0;
  animation-fill-mode: forwards;
}
html.is-loaded .c-home_monitor {
  animation: monitorAppear 0.9s cubic-bezier(0.165, 0.84, 0.44, 1) 500ms;
  animation-fill-mode: forwards;
}

@keyframes monitorAppear {
  0% {
    transform: translate3d(0, -50%, 0);
    opacity: 0;
  }
  100% {
    transform: translate3d(0, 0, 0);
    opacity: 1;
  }
}
.c-home_text {
  opacity: 0;
  align-self: flex-end;
  transform: translate3d(0, -2.5rem, 0);
  transition: transform 0.3s cubic-bezier(0.215, 0.61, 0.355, 1),
    opacity 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
}
html.is-ready .c-home_text {
  opacity: 1;
  transform: translate3d(0, 0, 0);
  transition: transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
@media (min-width: 1600px) {
  .c-home_text {
    width: calc((100vw - 13.125rem) * 3 / 20 + 1.25rem);
  }
}
@media (max-width: 1599px) and (min-width: 1000px) {
  .c-home_text {
    width: calc((100vw - 13.125rem) * 4 / 20 + 1.875rem);
  }
}
@media (max-width: 999px) {
  .c-home_text {
    width: 18.75rem;
  }
}

.c-home_ctas {
  display: flex;
  align-items: center;
  justify-content: center;
}

.c-home_ctas_item {
  margin: 2.5rem 0.625rem 0 0.625rem;
  height: auto;
  opacity: 0;
  transform: translate3d(0, 2.5rem, 0);
}
html.is-ready .c-home_ctas_item {
  opacity: 1;
  transform: translate3d(0, 0, 0);
  transition: transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
html.is-ready .c-home_ctas_item:nth-child(2) {
  transition-delay: 0.1s;
}

.c-home_ctas_link {
  display: inline-block;
  font-size: 7vw;
}
html[lang="fr"] .c-home_ctas_link {
  font-size: 6.5vw;
}
@media (max-width: 999px) {
  .c-home_ctas_link {
    font-size: 10.5vw;
  }
}

.c-socials {
  display: flex;
  align-items: center;
}

.c-socials_item {
  display: flex;
  margin: 0 0.1875rem;
}
.c-socials_item:first-child {
  margin-left: 0;
}
.c-socials_item:last-child {
  margin-right: 0;
}

.c-pill-image {
  display: inline-block;
  height: 0.714em;
  border-radius: 999em;
  width: 2em;
  overflow: hidden;
  background-size: cover;
  background-position: center center;
}

.c-text-lines {
  display: initial;
}

.c-text-lines_item {
  position: relative;
  display: block;
  overflow: hidden;
  padding-top: 0.2em;
  margin-top: -0.2em;
}
.c-text-lines_item.-allow-overflow {
  overflow: visible;
}

.c-text-lines_item_outer {
  display: inline-block;
  overflow: hidden;
  padding-top: 0.2em;
  margin-top: -0.2em;
  margin-bottom: -0.2em;
}

.c-text-lines_item_inner {
  display: inline-block;
  transform: translate3d(0, -120%, 0);
  transition: transform 0.9s cubic-bezier(0.23, 1, 0.32, 1);
  padding-top: 0.2em;
  margin-top: -0.2em;
}
html.is-ready .c-text-lines.is-inview .c-text-lines_item_inner {
  transform: translate3d(0, 0, 0);
}
html.is-ready
  .c-text-lines.is-inview
  .c-text-lines_item:nth-child(1)
  .c-text-lines_item_inner {
  transition-delay: 0s;
}
html.is-ready
  .c-text-lines.is-inview
  .c-text-lines_item:nth-child(2)
  .c-text-lines_item_inner {
  transition-delay: 0.135s;
}
html.is-ready
  .c-text-lines.is-inview
  .c-text-lines_item:nth-child(3)
  .c-text-lines_item_inner {
  transition-delay: 0.27s;
}
html.is-ready
  .c-text-lines.is-inview
  .c-text-lines_item:nth-child(4)
  .c-text-lines_item_inner {
  transition-delay: 0.405s;
}
html.is-ready
  .c-text-lines.is-inview
  .c-text-lines_item:nth-child(5)
  .c-text-lines_item_inner {
  transition-delay: 0.54s;
}

.c-circle {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
.c-circle svg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  stroke: #d3fd50;
  stroke-width: 2px;
  fill: transparent;
}

.c-dot {
  display: inline-block;
  width: 0.875rem;
  height: 0.875rem;
  border-radius: 50%;
  background-color: #000000;
}

@media (min-width: 700px) {
  .c-about-hero {
    padding-bottom: 7.5rem;
  }
}

.c-about-hero_intro {
  position: relative;
  padding-top: 55vh;
}
@media (max-aspect-ratio: 1/1) {
  .c-about-hero_intro {
    padding-top: 50vw;
  }
}
@media (min-width: 700px) {
  .c-about-hero_intro {
    padding-bottom: 12.5rem;
  }
}
@media (max-width: 699px) {
  .c-about-hero_intro {
    padding-bottom: 7.5rem;
  }
}

.c-about-hero_title {
  display: block;
  text-align: center;
  font-size: 20vw;
  text-transform: uppercase;
  line-height: 0.8666666667;
  text-align: center;
  padding-top: 0.05px;
  padding-bottom: 0.05px;
}
.c-about-hero_title::before {
  content: "";
  margin-top: 0.023em;
  display: block;
  height: 0;
}
@media (max-width: 699px) {
  .c-about-hero_title {
    font-size: 18vw;
  }
}
@media (max-aspect-ratio: 1/1) {
  .c-about-hero_title {
    margin-bottom: 7.5rem;
  }
}

.c-about-hero_sticky-area {
  position: absolute;
  top: 0;
  left: 0;
  bottom: -10rem;
  right: 0;
}

.c-about-hero_visual {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  z-index: -1;
}

.c-about-hero_visual_outer {
  display: block;
  overflow: hidden;
  border-radius: 1.25rem;
  position: absolute;
  bottom: 35vh;
  left: calc((100% - 11.875rem) * 6 / 20 + 3.75rem);
  width: calc((100% - 11.875rem) * 3 / 20 + 1.875rem);
  height: auto;
  transform: translate3d(-101%, 0, 0);
  transition: transform 0.9s cubic-bezier(0.215, 0.61, 0.355, 1) 0.3s;
}
@media (max-aspect-ratio: 1/1) {
  .c-about-hero_visual_outer {
    top: 30vw;
    bottom: initial;
  }
}
@media (max-width: 999px) {
  .c-about-hero_visual_outer {
    width: 20vw;
    left: 25%;
  }
}
.is-ready .c-about-hero_visual_outer {
  transform: translate3d(0, 0, 0);
}

.c-about-hero_visual_inner {
  overflow: hidden;
  border-radius: 1.25rem;
  transform: translate3d(101%, 0, 0);
  transition: transform 0.9s cubic-bezier(0.215, 0.61, 0.355, 1) 0.3s;
}
.c-about-hero_visual_inner:before {
  content: "";
  display: block;
  padding-bottom: 133.3333333333%;
}
.is-ready .c-about-hero_visual_inner {
  transform: translate3d(0, 0, 0);
}

.c-about-hero_visual_image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
}
.c-about-hero_visual_image:first-child {
  z-index: 1;
}

@media (min-width: 700px) {
  .c-about-hero_section {
    margin-bottom: 10rem;
  }
}
@media (max-width: 699px) {
  .c-about-hero_section {
    margin-bottom: 2.5rem;
  }
}

@media (min-width: 700px) {
  .c-about-hero_block {
    width: 37.5%;
  }
  .c-about-hero_block.-smaller {
    width: 25%;
  }
}
@media (max-width: 699px) {
  .c-about-hero_block {
    margin-bottom: 2.5rem;
  }
  .c-about-hero_block.-half-mobile {
    width: 50%;
  }
}
.c-about-hero_block.-padright {
  padding-right: 2.5rem;
}
.c-about-hero_block em {
  text-decoration: underline;
  font-style: normal;
}

.c-team {
  padding-top: 12.5rem;
  margin-bottom: 15rem;
}

.c-team_title {
  opacity: 0.5;
}

.c-team_list-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  justify-content: center;
}
html.has-scroll-smooth .c-team_list-wrapper {
  min-height: 70vh;
}

@media (max-width: 699px) {
  .c-team_list {
    padding: 0 0.625rem;
  }
}

.c-team_list_sticky-area {
  position: absolute;
  top: -30vh;
  bottom: -30vh;
  left: 0;
  right: 0;
}

.c-team_list_visuals {
  pointer-events: none;
  position: absolute;
  top: -30vh;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
}
html:not(.has-scroll-smooth) .c-team_list_visuals {
  display: none;
}

.c-team_list_visuals_outer {
  height: 60vh;
  width: calc((100vw - 13.125rem) * 5 / 20 + 2.5rem);
  height: calc(((100vw - 13.125rem) * 5 / 20 + 2.5rem) * 3 / 2);
  margin-left: calc((100vw - 13.125rem) * 5 / 20 + 3.75rem);
  transform: translate3d(-101%, 0, 0);
  transition: transform 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
  overflow: hidden;
}
.c-team_list_visuals.is-active .c-team_list_visuals_outer {
  transform: translate3d(0, 0, 0);
}

.c-team_list_visuals_inner {
  transform: translate3d(101%, 0, 0);
  border-radius: 1.25rem;
  transition: transform 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
  overflow: hidden;
}
.c-team_list_visuals.is-active .c-team_list_visuals_inner {
  transform: translate3d(0, 0, 0);
}

.c-team_list_image {
  position: relative;
  overflow: hidden;
  opacity: 0;
  transition: opacity 0s linear 0.3s;
  transform: translate3d(-100%, 0, 0);
}
.c-team_list_image img {
  transform: translate3d(100%, 0, 0);
  width: 100%;
}
.c-team_list_image.is-current {
  transition: none;
  opacity: 1;
}
.c-team_list_image.run-animation {
  animation: showTeamListImageWrapper 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
  animation-fill-mode: forwards;
}
.c-team_list_image.run-animation img {
  animation: showTeamListImage 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
  animation-fill-mode: forwards;
}
.c-team_list_image:not(:first-child) {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

@keyframes showTeamListImageWrapper {
  0% {
    transform: translate3d(-100%, 0, 0);
  }
  100% {
    transform: translate3d(0, 0, 0);
  }
}
@keyframes showTeamListImage {
  0% {
    transform: translate3d(100%, 0, 0);
  }
  100% {
    transform: translate3d(0, 0, 0);
  }
}
.c-team_row {
  position: relative;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.625rem;
  border-top: 1px solid;
  color: currentColor;
  transition: color 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.c-team_row:last-child {
  border-bottom: 1px solid;
}
.c-team_row:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #d3fd50;
  transform: scale3d(1, 0, 1);
  transition: transform 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
  transform-origin: top center;
  z-index: -1;
}
.c-team_row:focus,
.c-team_row:hover {
  color: #000000;
}
.c-team_row:focus:before,
.c-team_row:hover:before {
  transform: scale3d(1, 1, 1);
}

@media (min-width: 700px) {
  .c-team_row_position {
    align-self: flex-start;
  }
}
@media (max-width: 699px) {
  .c-team_row_position {
    color: #d3fd50;
    font-size: 10px;
    text-transform: uppercase;
  }
}

.c-team_row_name {
  display: inline-block;
  font-size: 2.5rem;
  text-transform: uppercase;
  line-height: 1;
  margin-bottom: -0.2em;
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}
.c-team_row_name::before {
  content: "";
  margin-top: 0.023em;
  display: block;
  height: 0;
}
@media (max-width: 699px) {
  .c-team_row_name {
    font-size: 16px;
    text-align: right;
    flex-shrink: 0;
    padding-left: 0.5em;
  }
}

.c-team_row_visual {
  display: none;
}

.c-team_featured {
  overflow: hidden;
  margin-bottom: 10rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.2);
}
@media (min-aspect-ratio: 1/1) {
  .c-team_featured {
    margin-top: -5rem;
  }
}

.c-team_featured_member {
  position: relative;
  display: block;
  width: 100vw;
  height: 100vh;
}
@media (max-aspect-ratio: 1/1) {
  .c-team_featured_member {
    height: 100vw;
  }
}

.c-team_featured_member_sticky {
  position: absolute;
  top: 0;
  left: 0;
  bottom: -100vh;
  right: 0;
}
@media (max-aspect-ratio: 1/1) {
  .c-team_featured_member_sticky {
    bottom: -100vw;
  }
}

.c-team_featured_member_image {
  position: absolute;
  background-size: cover;
  background-position: center center;
  overflow: hidden;
  border-radius: 1.25rem;
}
.c-team_featured_member_image img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  -o-object-fit: cover;
  object-fit: cover;
}
@media (min-aspect-ratio: 1/1) {
  html.has-scroll-smooth .c-team_featured_member_image {
    height: 100vh;
    width: 66.6666666667vh;
    top: 0;
    left: 50%;
    transform: translate(-50%, 0);
  }
}
@media (max-aspect-ratio: 1/1) {
  html.has-scroll-smooth .c-team_featured_member_image {
    top: 50vh;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 50vw;
    height: 75vw;
  }
}
@media (min-aspect-ratio: 1/1) {
  html:not(.has-scroll-smooth) .c-team_featured_member_image {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 50vh;
    height: 75vh;
  }
}
@media (max-aspect-ratio: 1/1) {
  html:not(.has-scroll-smooth) .c-team_featured_member_image {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 50vw;
    height: 75vw;
  }
}

.c-team_featured_member_content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: stretch;
}

.c-team_featured_member_line {
  display: flex;
  width: 100%;
}

.c-team_featured_member_line_inner {
  position: relative;
  display: flex;
  align-items: baseline;
  min-width: 100vw;
  white-space: nowrap;
  flex-shrink: 0;
  animation-duration: 8s;
  animation-timing-function: linear;
  animation-iteration-count: infinite;
  transition: opacity 0.2s cubic-bezier(0.215, 0.61, 0.355, 1);
  z-index: 10;
}
.c-team_featured_member_line_inner:after {
  content: " ";
}
@media (min-aspect-ratio: 1/1) {
  .c-team_featured_member_line_inner {
    opacity: 0;
  }
  .c-team_featured_member_line:nth-child(1) .c-team_featured_member_line_inner {
    z-index: 0;
  }
}
.c-team_featured_member_content.is-inview .c-team_featured_member_line_inner {
  opacity: 1;
}
.c-team_featured_member:nth-child(even)
  .c-team_featured_member_line:nth-child(odd)
  .c-team_featured_member_line_inner {
  padding-left: 50vw;
  animation-name: teamFeaturedMemberLineInner;
}
.c-team_featured_member:nth-child(even)
  .c-team_featured_member_line:nth-child(even)
  .c-team_featured_member_line_inner {
  padding-right: 50vw;
  animation-name: teamFeaturedMemberLineInnerReversed;
}
.c-team_featured_member:nth-child(odd)
  .c-team_featured_member_line:nth-child(even)
  .c-team_featured_member_line_inner {
  padding-left: 50vw;
  animation-name: teamFeaturedMemberLineInnerReversed;
}
.c-team_featured_member:nth-child(odd)
  .c-team_featured_member_line:nth-child(odd)
  .c-team_featured_member_line_inner {
  padding-right: 50vw;
  animation-name: teamFeaturedMemberLineInner;
}

@keyframes teamFeaturedMemberLineInner {
  0% {
    transform: translate3d(0, 0, 0);
  }
  100% {
    transform: translate3d(-100%, 0, 0);
  }
}
@keyframes teamFeaturedMemberLineInnerReversed {
  0% {
    transform: translate3d(-100%, 0, 0);
  }
  100% {
    transform: translate3d(0, 0, 0);
  }
}
.c-team_featured_member_name {
  font-size: 10vw;
  color: #d3fd50;
  text-transform: uppercase;
}

.c-team_featured_member_position {
  text-transform: uppercase;
  font-size: 1.875rem;
  margin: 0 3em;
}

.c-cta-slides {
  position: relative;
  color: #ffffff;
}

.c-cta-slides_head {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 30;
  display: flex;
  justify-content: center;
  pointer-events: none;
}

.c-cta-slides_global-link {
  text-transform: uppercase;
  font-size: 1.25rem;
  pointer-events: all;
  padding: 0.625rem;
}
.c-cta-slides_global-link:focus,
.c-cta-slides_global-link:hover {
  color: #d3fd50;
  text-decoration: underline;
}

.c-cta-slides_item {
  display: block;
  position: relative;
  margin-top: -5rem;
  border-radius: 2.5rem;
  overflow: hidden;
  border-bottom-right-radius: 0px;
  border-bottom-left-radius: 0px;
}
.c-cta-slides_item:first-child {
  margin-top: 0;
}

.c-cta-slides_item_outer {
  position: relative;
  display: block;
  width: 100vw;
  height: 100vh;
}

.c-cta-slides_item_sticky-area {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 200vh;
}

.c-cta-slides_visual {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  background-size: cover;
  transition: transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transform: scale3d(1, 1, 1);
}
.c-cta-slides_visual:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  background-color: #000000;
  opacity: 0.1;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.c-cta-slides_visual img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
}
.c-cta-slides_item:hover .c-cta-slides_visual,
.c-cta-slides_item:focus .c-cta-slides_visual {
  transform: scale3d(1.05, 1.05, 1);
}
.c-cta-slides_item:hover .c-cta-slides_visual:before,
.c-cta-slides_item:focus .c-cta-slides_visual:before {
  opacity: 0.2;
}

.c-cta-slides_content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 20;
  text-align: center;
  width: calc(100% - 1.25rem);
}

.c-cta-slides_title {
  font-size: 5rem;
}
.c-cta-slides_item:hover .c-cta-slides_title {
  text-decoration: underline;
}
@media (max-width: 699px) {
  .c-cta-slides_title {
    font-size: 3.125rem;
  }
}

.c-cta-slides_subtitle {
  font-size: 1.875rem;
}
@media (max-width: 699px) {
  .c-cta-slides_subtitle {
    font-size: 1.5rem;
  }
}

.c-footer {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 0.625rem;
  background-color: #000000;
  color: #ffffff;
}
@media (min-aspect-ratio: 1/1) {
  .c-footer {
    min-height: 62vh;
  }
}

@media (min-aspect-ratio: 1/1) {
  .c-footer_head {
    font-size: 5vw;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .c-footer_head .c-button {
    font-size: inherit;
  }
}

@media (max-aspect-ratio: 1/1) {
  .c-footer_socials {
    margin-bottom: 60vh;
  }
}

@media (max-aspect-ratio: 1/1) {
  .c-footer_contact {
    margin-bottom: 2.5rem;
  }
}
@media (max-aspect-ratio: 1/1) and (max-width: 699px) {
  .c-footer_contact {
    font-size: 14vw;
  }
}
@media (max-aspect-ratio: 1/1) and (min-width: 700px) {
  .c-footer_contact {
    font-size: 10vw;
  }
}

.c-footer_foot {
  display: grid;
  gap: 1.25rem 2.5rem;
  align-items: center;
}
@media (max-width: 999px) {
  .c-footer_foot {
    grid-template-columns: 1fr;
    justify-content: center;
  }
}
@media (min-width: 1000px) {
  .c-footer_foot {
    grid-template-columns: 1fr auto 1fr;
  }
}

.c-footer_top-btn-container {
  display: flex;
}
@media (max-width: 999px) {
  .c-footer_top-btn-container {
    justify-content: center;
  }
}
@media (min-width: 1000px) {
  .c-footer_top-btn-container {
    justify-content: flex-end;
  }
}

.c-footer_top-btn {
  text-transform: uppercase;
}
@media (min-width: 1400px) {
  .c-footer_top-btn {
    font-size: 1.25rem;
  }
}
.c-footer_top-btn:focus,
.c-footer_top-btn:hover {
  color: #d3fd50;
}

@media (max-width: 999px) {
  .c-footer_clock {
    display: flex;
    justify-content: center;
  }
}

.c-footer_legals {
  display: flex;
}
@media (min-width: 700px) {
  .c-footer_legals {
    justify-content: center;
    gap: 2.5rem;
  }
}
@media (max-width: 699px) {
  .c-footer_legals {
    flex-direction: column;
    text-align: center;
  }
}
.c-footer_legals li {
  display: flex;
}

.c-footer_legals_link {
  display: inline-block;
  text-transform: uppercase;
  line-height: 1.3;
}
@media (max-width: 699px) {
  .c-footer_legals_link {
    font-size: 0.75rem;
  }
}
.c-footer_legals_link:focus,
.c-footer_legals_link:hover {
  color: #d3fd50;
}

.c-page-head {
  padding-top: 50vh;
}
.c-page-head.-smaller-padding {
  padding-top: 12.5rem;
}

.c-work-thumb {
  position: relative;
}
@media (hover: none) {
  .c-work-thumb {
    display: flex;
    flex-direction: column;
    margin-bottom: 2.5rem;
  }
}

.c-work-thumb_visual {
  order: 1;
  position: relative;
  background-color: rgba(0, 0, 0, 0.1);
}
.c-work-thumb_visual img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
  transform: scale3d(1, 1, 1);
  transition: transform 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.c-work-thumb_visual:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #000000;
  opacity: 0;
  z-index: 10;
  transition: opacity 0.2s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.c-work-thumb_link:hover + .c-work-thumb_visual:after,
.c-work-thumb_link:focus + .c-work-thumb_visual:after {
  opacity: 0.2;
}
.c-work-thumb_link:hover + .c-work-thumb_visual img,
.c-work-thumb_link:focus + .c-work-thumb_visual img {
  transform: scale3d(1.05, 1.05, 1);
}

.c-work-thumb_content {
  order: 2;
}
@media (hover: hover) {
  .c-work-thumb_content {
    position: absolute;
    visibility: hidden;
  }
}
@media (hover: none) {
  .c-work-thumb_content {
    position: relative;
    margin-top: 0.625rem;
    margin-left: 0.625rem;
    margin-right: 0.625rem;
  }
}
@media (max-width: 699px) {
  .c-work-thumb_content {
    font-size: 16px;
  }
}

.c-work-thumb_title {
  margin-bottom: 0;
  padding-right: 5rem;
}

.c-work-thumb_tag {
  position: absolute;
  top: 0.12em;
  right: 0;
}

.c-work-thumb_link {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 20;
  order: 3;
}
@media (hover: hover) {
  .c-work-thumb_link {
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.2s cubic-bezier(0.215, 0.61, 0.355, 1);
    color: #ffffff;
  }
  .c-work-thumb_link:hover,
  .c-work-thumb_link:focus {
    opacity: 1;
    color: #ffffff;
  }
  .c-work-thumb_link span {
    pointer-events: none;
    font-size: 3.75rem;
  }
}
@media (hover: hover) and (max-width: 999px) {
  .c-work-thumb_link span {
    font-size: 2.5rem;
  }
}
@media (hover: none) {
  .c-work-thumb_link .c-button {
    opacity: 0;
  }
}

.c-elastic-list {
  overflow: hidden;
}
html.is-loaded:not(.is-ready) .c-elastic-list {
  opacity: 0;
}
html.is-ready .c-elastic-list {
  opacity: 1;
  transition: opacity 0s linear 0.6s;
  animation: elasticListShow 0.6s cubic-bezier(0.215, 0.61, 0.355, 1) 0.1s;
  animation-fill-mode: forwards;
}
@media (max-width: 699px) {
  .c-elastic-list {
    margin-left: -0.625rem;
    margin-right: -0.625rem;
  }
}

.c-elastic-list_item {
  display: inline-flex;
  margin-bottom: 0.625rem;
  overflow: hidden;
  transform: translate3d(0, 0, 0);
  will-change: height;
}

.c-elastic-list_item_outer {
  overflow: hidden;
  display: inline-flex;
  width: 100%;
}
@media (hover: hover) {
  .c-elastic-list_item_outer {
    transition: border-radius 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  .c-elastic-list_item_outer:hover,
  .c-elastic-list_item_outer:focus {
    border-radius: 2.5rem;
  }
}

.c-elastic-list_item_inner {
  width: 100%;
}

@keyframes elasticListShow {
  0% {
    opacity: 0;
    transform: translate3d(0, -2.5rem, 0);
  }
  100% {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}
.c-project_year span:before {
  content: "⬤";
  font-size: 65%;
  vertical-align: baseline;
  margin-right: 0.2em;
  display: inline-block;
  transform: translate3d(0, -0.27em, 0);
}
@media (min-width: 700px) {
  .c-project_year {
    width: calc((100vw - 13.125rem) * 7 / 20 + 4.375rem);
  }
  .c-project_year span {
    padding-left: calc((100vw - 13.125rem) * 2 / 20 + 1.25rem);
  }
}
@media (max-width: 699px) {
  .c-project_year {
    padding-bottom: 5rem;
  }
}

@media (min-width: 700px) {
  .c-project_title {
    width: calc((100vw - 13.125rem) * 13 / 20 + 7.5rem);
    padding-right: calc((100vw - 13.125rem) * 2 / 20 + 1.25rem);
  }
  .c-project_title .c-text-lines_item {
    padding-right: calc((100vw - 13.125rem) * 2 / 20 + 1.25rem);
    margin-right: calc((100vw - 13.125rem) * -2 / 20 + -1.25rem);
  }
}
.c-project_title .c-button {
  font-size: 70%;
  margin-top: -0.3em;
}

.c-project_footer_text {
  display: block;
  overflow: hidden;
  width: 100vw;
}
.c-project_footer_text span {
  font-size: 40vw;
  margin-left: -13vw;
  white-space: nowrap;
}

.c-project_footer_visual {
  margin: auto;
  margin-top: -35vw;
  border-radius: 1.875rem;
  overflow: hidden;
}
@media (min-width: 700px) {
  .c-project_footer_visual {
    width: calc((100vw - 13.125rem) * 6 / 20 + 3.75rem);
  }
}
@media (max-width: 699px) {
  .c-project_footer_visual {
    width: 40vw;
  }
}
.c-project_footer_visual img {
  width: 100%;
}

.c-project-summary {
  position: relative;
  background-color: white;
  margin-bottom: 10rem;
}
@media (max-width: 699px) {
  .c-project-summary {
    margin-bottom: 5rem;
  }
}

.c-project-summary_visual-wrapper {
  position: relative;
  z-index: 1;
}
@media (min-width: 700px) {
  .c-project-summary_visual-wrapper {
    width: calc((100vw - 13.125rem) * 1 / 20 + 0.625rem);
  }
}
.c-project-summary_visual-wrapper span {
  position: absolute;
  top: 50%;
  right: 0;
  z-index: 100;
  mix-blend-mode: difference;
  color: white;
}

.c-project-summary_visual {
  overflow: hidden;
  transform: translate3d(-105%, 0, 0);
}
html.is-ready .c-project-summary_visual {
  transform: translate3d(0, 0, 0);
  transition: transform 1.5s cubic-bezier(0.215, 0.61, 0.355, 1);
}
@media (min-width: 700px) {
  .c-project-summary_visual {
    margin-right: -0.625rem;
    margin-left: calc((100vw - 13.125rem) * -12 / 20 + -7.5rem);
  }
}
@media (max-width: 699px) {
  .c-project-summary_visual {
    margin-right: -0.625rem;
    margin-left: -0.625rem;
  }
}
html.has-scroll-smooth .c-project-summary_visual img {
  width: 100%;
  margin-top: -2.5rem;
  margin-bottom: -2.5rem;
}

.c-project-summary_visual_outer {
  overflow: hidden;
  transform: translate3d(100%, 0, 0);
}
@media (min-width: 700px) {
  .c-project-summary_visual_outer {
    border-radius: 3.5rem;
  }
}
@media (max-width: 699px) {
  .c-project-summary_visual_outer {
    border-radius: 1.875rem;
  }
}
html.is-ready .c-project-summary_visual_outer {
  transform: translate3d(0, 0, 0);
  transition: transform 1.5s cubic-bezier(0.215, 0.61, 0.355, 1);
}

@media (min-width: 700px) {
  .c-project-summary_content {
    width: calc((100vw - 13.125rem) * 19 / 20 + 11.875rem);
    padding-top: 7.5rem;
    z-index: 10;
  }
}
@media (max-width: 699px) {
  .c-project-summary_content {
    padding-top: 2.5rem;
  }
}

.c-project-summary_punchline {
  position: relative;
  z-index: 20;
  mix-blend-mode: difference;
  color: #ffffff;
  display: block;
}
@media (min-width: 700px) {
  .c-project-summary_punchline {
    padding-right: calc((100vw - 13.125rem) * 8 / 20 + 5rem);
  }
  .c-project-summary_punchline .c-text-lines_item {
    margin-right: calc((100vw - 13.125rem) * -8 / 20 + -5rem);
    padding-right: calc((100vw - 13.125rem) * 8 / 20 + 5rem);
  }
}
@media (max-width: 699px) {
  .c-project-summary_punchline {
    margin-bottom: 2.5rem;
    font-size: 3.125rem;
  }
}

.c-project-summary_list {
  counter-reset: li;
}
@media (max-width: 699px) {
  .c-project-summary_list {
    margin-bottom: 5rem;
  }
}
.c-project-summary_list li:before {
  display: inline-block;
  counter-increment: li;
  content: counter(li);
}
@media (min-width: 700px) {
  .c-project-summary_list li:before {
    width: calc((100vw - 13.125rem) * 2 / 20 + 1.25rem);
  }
}
@media (max-width: 699px) {
  .c-project-summary_list li:before {
    width: 5rem;
  }
}
.c-project-summary_list a:focus,
.c-project-summary_list a:hover {
  text-decoration: underline;
}

.c-project-summary_text {
  padding-top: 0.625rem;
}
@media (min-width: 700px) {
  .c-project-summary_text p {
    padding-left: calc((100vw - 13.125rem) * 2 / 20 + 1.25rem);
    padding-right: calc((100vw - 13.125rem) * 2 / 20 + 1.25rem);
    text-indent: calc((100vw - 13.125rem) * 5 / 20 + 3.125rem);
  }
}
@media (max-width: 699px) {
  .c-project-summary_text p {
    text-indent: 5rem;
  }
}

.c-image-gallery {
  cursor: pointer;
}

.c-image-gallery_slide {
  position: relative;
}

.c-image-gallery_slide_inner {
  position: relative;
  overflow: hidden;
  transition: transform 0.5s cubic-bezier(0.215, 0.61, 0.355, 1),
    border-radius 0.5s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.c-image-gallery_slide_inner:before {
  display: block;
  content: "";
  padding-bottom: 56.25%;
  background-color: #f1f1f1;
}
.c-image-gallery_slide_inner img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
}
.c-image-gallery_slide.-radius .c-image-gallery_slide_inner {
  border-top-right-radius: 1.875rem;
  border-bottom-right-radius: 1.875rem;
}
@media (hover: none) {
  .c-image-gallery_slide.swiper-slide-active .c-image-gallery_slide_inner {
    transform: translateX(-0.625rem);
  }
  .c-image-gallery_slide.swiper-slide-active .c-image-gallery_slide_inner {
    border-top-right-radius: 1.875rem;
    border-bottom-right-radius: 1.875rem;
  }
}

.c-image-gallery_cursor {
  position: absolute;
  top: 0;
  left: 0;
  width: 15vw;
  height: 15vw;
  z-index: 100;
  mix-blend-mode: difference;
  pointer-events: none;
  opacity: 0;
}
@media (hover: none) {
  .c-image-gallery_cursor {
    display: none;
  }
}
.c-image-gallery_cursor svg {
  stroke: #ffffff;
  transform: translate(-50%, -50%);
  transition: transform 0.15s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.c-image-gallery_cursor.-visible {
  opacity: 1;
}
.c-image-gallery_cursor.-rotate svg {
  transform: translate(-50%, -50%) scaleX(-1);
}

.c-layered-punchline {
  position: relative;
  overflow: hidden;
}

.c-layered-punchline_text {
  text-transform: uppercase;
  line-height: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
@media (min-aspect-ratio: 1/1) {
  .c-layered-punchline_text {
    font-size: 9vw;
  }
}
@media (max-aspect-ratio: 1/1) {
  .c-layered-punchline_text {
    font-size: 11vw;
  }
}
.c-layered-punchline_text > div {
  overflow: hidden;
  margin-bottom: -0.1em;
}
.c-layered-punchline_text > div:nth-child(even) {
  text-align: right;
}
.c-layered-punchline_text > div > span {
  display: block;
  transform: translate3d(0, 100%, 0);
  transition: transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.c-layered-punchline.is-inview .c-layered-punchline_text > div > span {
  transform: translate3d(0, 0, 0);
}
.c-layered-punchline_text > div:nth-child(1) > span {
  transition-delay: 0.12s;
}
.c-layered-punchline_text > div:nth-child(2) > span {
  transition-delay: 0.24s;
}
.c-layered-punchline_text > div:nth-child(3) > span {
  transition-delay: 0.36s;
}
.c-layered-punchline_text > div:nth-child(4) > span {
  transition-delay: 0.48s;
}
html:not(.has-scroll-smooth) .c-layered-punchline_text.-above {
  display: none;
}
html.has-scroll-smooth .c-layered-punchline_text {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
}
html.has-scroll-smooth .c-layered-punchline_text:not(.-above) div:nth-child(2),
html.has-scroll-smooth .c-layered-punchline_text:not(.-above) div:nth-child(3) {
  opacity: 0;
}
html.has-scroll-smooth .c-layered-punchline_text.-above {
  z-index: 20;
}
html.has-scroll-smooth .c-layered-punchline_text.-above div:nth-child(1),
html.has-scroll-smooth .c-layered-punchline_text.-above div:nth-child(4) {
  opacity: 0;
}

.c-layered-punchline_images {
  position: relative;
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: auto;
  border-bottom-left-radius: 3.5rem;
  border-bottom-right-radius: 3.5rem;
  overflow: hidden;
}
html.has-scroll-smooth .c-layered-punchline_images {
  padding-top: 100vh;
  width: 66.6666666667vh;
}
@media (max-aspect-ratio: 1/1) {
  html.has-scroll-smooth .c-layered-punchline_images {
    width: 100vw;
  }
}
html:not(.has-scroll-smooth) .c-layered-punchline_images {
  width: 70vw;
}

.c-layered-punchline_images_item {
  position: relative;
}

.c-layered-punchline_images_sticky-area {
  position: absolute;
  top: 0;
  left: 0;
  width: 100vw;
  height: 300vh;
}
.c-layered-punchline_images_item:last-child
  .c-layered-punchline_images_sticky-area {
  height: 100vh;
}

.c-layered-punchline_image {
  position: relative;
  z-index: 10;
  overflow: hidden;
  transition: border-radius 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.c-layered-punchline_image img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
}
html.has-scroll-smooth .c-layered-punchline_image {
  height: 100vh;
  width: 66.6666666667vh;
  border-top-left-radius: 3.5rem;
  border-top-right-radius: 3.5rem;
}
@media (max-aspect-ratio: 1/1) {
  html.has-scroll-smooth .c-layered-punchline_image {
    width: 100vw;
    height: 100vh;
  }
}
html:not(.has-scroll-smooth) .c-layered-punchline_image {
  width: 70vw;
  height: 105vw;
  margin-bottom: 2.5rem;
}
@media (min-aspect-ratio: 1/1) {
  html:not(.has-scroll-smooth) .c-layered-punchline_image {
    height: 100vh;
    width: 66.6666666667vh;
  }
}
.c-layered-punchline_image.is-inview {
  border-radius: 0px;
}

.c-block-image video,
.c-block-image img {
  width: 100%;
}
.c-block-image.-rounded {
  border-radius: 3.5rem;
  overflow: hidden;
}
.c-block-image.-padded {
  width: calc((100vw - 13.125rem) * 14 / 20 + 8.125rem);
  margin: auto;
}
@media (max-width: 999px) {
  .c-block-image.-padded {
    width: 80%;
  }
}
@media (max-width: 699px) {
  .c-block-image.-padded {
    width: calc(100vw - 5rem);
  }
}

.c-block-text {
  width: calc((100vw - 13.125rem) * 16 / 20 + 9.375rem);
  text-indent: calc((100vw - 13.125rem) * 5 / 20 + 3.125rem);
  margin: auto;
}
@media (max-width: 699px) {
  .c-block-text {
    width: 100%;
    text-indent: 5rem;
  }
}

.c-caption-gallery {
  padding-top: 0.625rem;
  padding-right: 0.625rem;
  background-color: #ffffff;
  cursor: pointer;
}
.c-caption-gallery img {
  margin-bottom: 0.625rem;
  width: 100%;
}

.c-caption-gallery_slide {
  box-sizing: border-box;
}
@media (min-width: 700px) {
  .c-caption-gallery_slide {
    width: calc((100vw - 13.125rem) * 10 / 20 + 6.25rem);
  }
}
@media (max-width: 699px) {
  .c-caption-gallery_slide {
    width: 75%;
  }
}

.c-caption-gallery_slide_inner {
  padding-left: 0.625rem;
}

.c-caption-gallery_content {
  display: flex;
}
.c-caption-gallery_content > * {
  box-sizing: border-box;
}

.c-caption-gallery_index {
  flex-shrink: 0;
}
@media (min-width: 700px) {
  .c-caption-gallery_index {
    width: 40%;
  }
}
@media (max-width: 699px) {
  .c-caption-gallery_index {
    width: 25%;
  }
}

.c-caption-gallery_text {
  flex-shrink: 0;
  padding-right: calc((100vw - 13.125rem) * 1 / 20 + 0.625rem);
}
@media (min-width: 700px) {
  .c-caption-gallery_text {
    width: 60%;
  }
}
@media (max-width: 699px) {
  .c-caption-gallery_text {
    width: 75%;
  }
}

.c-caption-gallery_cursor {
  position: absolute;
  top: 0;
  left: 0;
  width: 15vw;
  height: 15vw;
  z-index: 100;
  mix-blend-mode: difference;
  pointer-events: none;
  opacity: 0;
}
@media (hover: none) {
  .c-caption-gallery_cursor {
    display: none;
  }
}
.c-caption-gallery_cursor svg {
  stroke: #ffffff;
  transform: translate(-50%, -50%);
  transition: transform 0.15s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.c-caption-gallery_cursor.-visible {
  opacity: 1;
}
.c-caption-gallery_cursor.-rotate svg {
  transform: translate(-50%, -50%) scaleX(-1);
}

.c-video-featured {
  position: relative;
  border-top: 1px solid;
  border-bottom: 1px solid;
}
.c-video-featured:before {
  content: "";
  display: block;
  padding-bottom: 56.25%;
}
@media (max-width: 1199px) {
  .c-video-featured {
    padding-top: 2.5rem;
    padding-bottom: 2.5rem;
  }
}
@media (max-aspect-ratio: 1/1) {
  .c-video-featured:before {
    padding-bottom: 100%;
  }
}

.c-video-featured_head {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: space-between;
  text-transform: uppercase;
  padding: 0.625rem;
}

.c-video-featured_title {
  font-size: 1.875rem;
}
@media (max-width: 699px) {
  .c-video-featured_title {
    font-size: 1.25rem;
  }
}

.c-video-featured_subtitle {
  font-size: 1.875rem;
}
@media (max-width: 699px) {
  .c-video-featured_subtitle {
    font-size: 1.25rem;
  }
}
.c-video-featured_subtitle:before {
  content: "_";
}

.c-video-featured_visual {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 56.25vw;
  height: 56.25vw;
  transform: translate3d(-50%, -50%, 0);
  border-radius: 100%;
  overflow: hidden;
  transition-property: width, height, border-radius;
  transition-duration: 0.6s;
  transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  z-index: 10;
}
.c-video-featured_visual img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
  z-index: 1;
}
.c-video-featured_visual video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
  z-index: 2;
}
.c-video-featured.-hover .c-video-featured_visual {
  width: 100%;
  height: 100%;
  border-radius: 0px;
  will-change: width, height, border-radius;
}
@media (max-aspect-ratio: 1/1) {
  .c-video-featured_visual {
    width: 90vw;
    height: 90vw;
  }
}

.c-video-featured_play {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 56.25vw;
  height: 56.25vw;
  transform: translate3d(-50%, -50%, 0);
  border-radius: 50%;
  overflow: hidden;
  z-index: 30;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 8vw;
  text-transform: uppercase;
}
.c-video-featured_play:focus,
.c-video-featured_play:hover {
  text-decoration: underline;
}

.c-video-featured_duration {
  position: absolute;
  bottom: 0;
  right: 0;
  font-size: 6.25rem;
  line-height: 0.8;
  z-index: 1;
}
@media (max-width: 699px) {
  .c-video-featured_duration {
    font-size: 2.5rem;
  }
}

@media (min-width: 1200px) {
  .c-video-gallery {
    display: flex;
    padding: 0.625rem 0.3125rem;
  }
}
.c-video-gallery_item {
  display: flex;
  overflow: hidden;
  flex-direction: column;
  flex-grow: 1;
  transition: flex 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.c-video-gallery_item.-hover {
  flex-grow: 2;
}
@media (min-width: 1200px) {
  .c-video-gallery_item {
    height: 80vh;
    margin: 0 0.3125rem;
  }
}
@media (max-width: 1199px) {
  .c-video-gallery_item {
    height: 62.5vw;
    margin: 0.625rem 0.625rem;
  }
}

.c-video-gallery_visual {
  position: relative;
  flex-grow: 1;
  overflow: hidden;
  transition: border-radius 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.c-video-gallery_visual img,
.c-video-gallery_visual video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
  z-index: 1;
}
.c-video-gallery_item.-hover .c-video-gallery_visual {
  border-radius: 3.5rem;
}
@media (max-width: 1199px) {
  .c-video-gallery_visual {
    border-radius: 1.875rem;
  }
}

.c-video-gallery_play {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
}
@media (min-width: 1200px) {
  .c-video-gallery_play span {
    position: absolute;
    bottom: 2.5rem;
    left: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #ffffff;
    border-radius: 50%;
    width: 12.5rem;
    height: 12.5rem;
    text-transform: uppercase;
    transform: translate3d(0%, 0%, 0) scale3d(0.2, 0.2, 1);
    opacity: 0;
    transition-property: transform, opacity;
    transition-duration: 0.3s, 0.15s;
    transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
  }
  .c-video-gallery_item.-hover .c-video-gallery_play span {
    opacity: 1;
    transform: translate3d(0%, 0%, 0) scale3d(1, 1, 1);
  }
}
@media (max-width: 1199px) {
  .c-video-gallery_play {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .c-video-gallery_play span {
    color: #ffffff;
    font-size: 10vw;
    text-transform: uppercase;
  }
}

.c-video-gallery_head {
  text-transform: uppercase;
  display: flex;
  justify-content: space-between;
  flex-shrink: 0;
  padding-top: 0.625rem;
}
@media (max-width: 1199px) {
  .c-video-gallery_head {
    border-bottom: 1px solid;
  }
}

.c-video-gallery_title:before {
  content: "_";
}

.c-video-gallery_duration {
  opacity: 0;
  transition: opacity 0.15s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.c-video-gallery_item.-hover .c-video-gallery_duration {
  opacity: 1;
}

.c-video-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}
.c-video-modal.is-active {
  pointer-events: all;
}

.c-video-modal_bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #1d1d1d;
  transition: transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    background 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transform: scale3d(1, 0, 1);
  transform-origin: top center;
  opacity: 0.95;
}
.c-video-modal.is-active .c-video-modal_bg {
  transform: scale3d(1, 1, 1);
}

.c-video-modal_content {
  width: 80%;
}
@media (max-width: 1199px) {
  .c-video-modal_content {
    width: 90%;
  }
}

.c-video-modal_inner {
  position: relative;
  padding-bottom: 56%;
  background-color: #000000;
  opacity: 0;
  transition: opacity 0.25s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 0s;
}
.c-video-modal.is-active .c-video-modal_inner {
  opacity: 1;
  transition: opacity 1s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 0.6s;
}
.c-video-modal_inner iframe,
.c-video-modal_inner video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.c-video-modal_close {
  position: absolute;
  top: 0.3125rem;
  right: 0.3125rem;
  color: #ffffff;
  transition: transform 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
  transform: translateY(-100%) translateY(-3.75rem);
}
.c-video-modal.is-active .c-video-modal_close {
  transform: translateY(0);
}

.c-fancy-gallery_head {
  position: relative;
  display: flex;
  margin-top: 5rem;
}

.c-fancy-gallery_head_small {
  position: relative;
  z-index: 10;
  width: calc((100vw - 13.125rem) * 9 / 20 + 7.5rem);
  padding-left: calc((100vw - 13.125rem) * 1 / 20 + 1.25rem);
  margin-top: 30%;
  flex-shrink: 0;
}
@media (max-width: 999px) {
  .c-fancy-gallery_head_small {
    width: 40vw;
    padding-left: 0;
  }
}
.c-fancy-gallery_head_small img {
  width: 100%;
}

.c-fancy-gallery_head_large {
  overflow: hidden;
  flex-grow: 1;
  margin-left: calc((100vw - 13.125rem) * -3 / 20 + -1.875rem);
  border-top-left-radius: 3.5rem;
  border-bottom-left-radius: 3.5rem;
}
@media (max-width: 699px) {
  .c-fancy-gallery_head_large {
    margin-left: -5rem;
    padding-left: 0;
  }
}
.c-fancy-gallery_head_large img {
  width: 100%;
  margin-top: -2.5rem;
  margin-bottom: -2.5rem;
}

.c-fancy-gallery_container {
  margin-top: -10rem;
  padding-top: 20rem;
  padding-bottom: 10rem;
}
@media (max-width: 699px) {
  .c-fancy-gallery_container {
    margin-top: -5rem;
    padding-top: 7.5rem;
    padding-bottom: 2.5rem;
  }
}

.c-fancy-gallery_inner {
  width: calc((100vw - 13.125rem) * 12 / 20 + 7.5rem);
  margin: auto;
}
@media (max-width: 999px) {
  .c-fancy-gallery_inner {
    width: 80%;
  }
}
@media (max-width: 699px) {
  .c-fancy-gallery_inner {
    width: calc(100% - 5rem);
  }
}

.c-fancy-gallery_intro {
  margin-bottom: 7.5rem;
}
@media (max-width: 699px) {
  .c-fancy-gallery_intro {
    margin-bottom: 2.5rem;
  }
}

.c-stack {
  position: relative;
}

.c-stack_item {
  position: relative;
  margin-bottom: 5rem;
}
@media (max-width: 699px) {
  .c-stack_item {
    margin-bottom: 2.5rem;
  }
}

.c-stack_visual {
  position: relative;
}
.c-stack_visual:before {
  content: "";
  display: block;
  padding-bottom: 60.6060606061%;
}
.c-stack_visual img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
}

.c-stack_item_area {
  outline: 1px solid red;
}

.c-accordion {
  border-top: 1px solid;
}

.c-accordion_item {
  position: relative;
  border-bottom: 1px solid;
}

.c-accordion_header {
  padding: 1.5rem 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}
.c-accordion_header:hover,
.c-accordion_header:focus {
  outline: none;
}
.c-accordion_header.focus-visible {
  outline-color: #d3fd50;
  outline-style: auto;
  outline-width: 5px;
}

.c-accordion_heading {
  font-size: 9vw;
  transition: opacity 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
  opacity: 1;
  line-height: 0.8666666667;
  margin-bottom: -0.2em;
}
.c-accordion_header:hover .c-accordion_heading {
  opacity: 0.7;
}

.c-accordion_icon {
  position: relative;
  width: 5vw;
  height: 5vw;
  margin-right: 0.625rem;
}
.c-accordion_icon::before,
.c-accordion_icon::after {
  content: "";
  position: absolute;
}
.c-accordion_icon::before {
  border-top: 2px solid;
  top: 50%;
  right: 0;
  left: 0;
  transform: translateY(-50%);
}
.c-accordion_icon::after {
  border-left: 2px solid;
  top: 0;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  transition: transform 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.c-accordion_item.is-active .c-accordion_icon::after {
  transform: translateX(-50%) scaleY(0);
}

.c-accordion_main img {
  width: 100%;
}
.c-accordion_item:not(.is-active) > .c-accordion_main {
  display: none;
}

.c-audio-sample {
  position: relative;
}

.c-audio-sample_visual {
  position: relative;
}
.c-audio-sample_visual img {
  width: 100%;
}

.c-audio-sample_button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 7vw;
  z-index: 20;
  overflow: hidden;
}
.c-audio-sample_button:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  background-color: #ffffff;
}
.c-audio-sample_button:focus,
.c-audio-sample_button:hover {
  color: rgba(0, 0, 0, 0.5);
}
.c-audio-sample_button > * {
  pointer-events: none;
}

.c-audio-sample_spinner {
  display: none;
  width: 0.6em;
  height: 0.6em;
  justify-content: space-between;
}
.c-audio-sample_spinner span {
  width: 25%;
  height: 100%;
  background-color: currentColor;
  animation: audioSamplePulse 0.6s cubic-bezier(0.215, 0.61, 0.355, 1) infinite;
}
.c-audio-sample_spinner span:nth-child(1) {
  animation-delay: -0.12s;
}
.c-audio-sample_spinner span:nth-child(2) {
  animation-delay: -0.24s;
}
.c-audio-sample_spinner span:nth-child(3) {
  animation-delay: -0.36s;
}
.c-audio-sample.-playing .c-audio-sample_spinner {
  display: inline-flex;
}

@keyframes audioSamplePulse {
  0% {
    transform: scale3d(1, 0.2, 1);
  }
  50% {
    transform: scale3d(1, 1, 1);
  }
  100% {
    transform: scale3d(1, 0.2, 1);
  }
}
@media (max-width: 999px) {
  .c-contact {
    padding-top: 6.25rem;
  }
}

.c-contact_section {
  margin-bottom: 10rem;
}
.c-contact_section.-last {
  margin-bottom: 0;
}

.c-contact_punchline {
  display: block;
  padding-top: 0.625rem;
  width: 100%;
  font-size: 10vw;
  text-transform: uppercase;
  font-weight: 300;
  line-height: 0.8666666667;
  text-align: center;
  padding: 0 10vw;
  padding-top: 0.625rem;
  margin-bottom: -0.1666em;
  padding-bottom: 0.05px;
}
.c-contact_punchline::before {
  content: "";
  margin-top: 0.023em;
  display: block;
  height: 0;
}
.c-contact_punchline .c-button {
  font-size: 80%;
}
@media (max-width: 699px) {
  .c-contact_punchline {
    font-size: 12vw;
    padding-left: 0;
    padding-right: 0;
    position: relative;
    flex-grow: 0;
    margin-bottom: 2.5rem;
  }
}

.c-contact_infos {
  position: relative;
}
@media (min-width: 700px) {
  .c-contact_infos {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
    z-index: 10;
    pointer-events: none;
    transform: translateY(-100%);
    margin-top: 0.3em;
  }
}

.c-contact_infos_col {
  text-align: center;
  pointer-events: all;
}
.c-contact_infos_col a {
  display: block;
}
.c-contact_infos_col a:focus,
.c-contact_infos_col a:hover {
  color: #d3fd50;
  text-decoration: underline;
}
@media (min-width: 700px) {
  .c-contact_infos_col {
    width: calc((100vw - 13.125rem) * 5 / 20 + 3.125rem);
  }
}
@media (max-width: 699px) {
  .c-contact_infos_col {
    margin: 2.5rem 0 0 0;
  }
}

@media (min-width: 1000px) {
  .c-contact_banner {
    margin: 5rem 0 5rem 0;
  }
}
@media (max-width: 999px) {
  .c-contact_banner {
    margin: 5rem 0 5rem 0;
  }
}
@media (max-width: 699px) {
  .c-contact_banner {
    margin: 2.5rem 0;
  }
}

.c-contact_happyface {
  position: absolute;
  top: 0;
  right: 25%;
  transform: translate(50%, -50%);
}
.c-contact_happyface svg {
  width: 0.75em;
  height: 0.75em;
  transform: rotate(45deg);
  transition: transform 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
}
html[data-scroll-direction="up"] .c-contact_happyface svg {
  transform: rotate(-15deg);
}

.c-contact_socials {
  text-align: center;
}
@media (min-width: 700px) {
  .c-contact_socials {
    margin-top: 5rem;
  }
}

.c-contact_socials_title {
  text-transform: uppercase;
  margin-bottom: 1.25rem;
}

.c-contact_socials_list {
  display: inline-flex;
  margin: auto;
  width: auto;
}
@media (min-aspect-ratio: 1/1) {
  .c-contact_socials_list {
    font-size: 5vw;
  }
}
@media (max-aspect-ratio: 1/1) {
  .c-contact_socials_list {
    font-size: 3.75rem;
  }
}
@media (max-width: 699px) {
  .c-contact_socials_list {
    font-size: 10vw;
  }
}
.c-contact_socials_list .c-button {
  font-size: inherit;
}

.c-banner {
  background-color: #d3fd50;
  color: #000000;
  overflow: hidden;
  width: 120vw;
  display: flex;
  align-items: center;
  font-size: 11vw;
  margin-left: -10vw;
  transform: rotate(-5deg);
  transition: transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
html[data-scroll-direction="up"] .c-banner {
  transform: rotate(5deg);
}
.c-banner:before {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #ffffff;
  transform: scale3d(1, 0, 1);
  transform-origin: bottom center;
  transition: transform 0.2s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.c-banner:focus,
.c-banner:hover {
  color: #000000;
}
.c-banner:focus:before,
.c-banner:hover:before {
  transform: scale3d(1, 1, 1);
}

.c-banner_inner {
  white-space: nowrap;
  line-height: 0.75;
  padding-top: 0.2em;
  text-transform: uppercase;
  flex-shrink: 0;
  animation: bannerInner 5s linear infinite;
}
.c-banner_inner svg {
  display: inline-block;
  margin-top: -0.2em;
  width: 1em;
  height: 0.714em;
}

.c-banner_wrapper {
  width: 100vw;
  overflow: hidden;
}

@keyframes bannerInner {
  0% {
    transform: translate3d(0, 0, 0);
  }
  100% {
    transform: translate3d(-100%, 0, 0);
  }
}
.c-preview-bar {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  pointer-events: none;
  z-index: 600;
  background-color: #ffffff;
  transform: translate3d(0, -100%, 0);
  padding-top: 3.125rem;
  overflow: hidden;
  transition: transform 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
  border-bottom: 2px solid;
}
.c-preview-bar:before {
  content: "";
  display: block;
  width: 100%;
  border-top: 2px solid;
  transform: scale3d(0, 1, 1);
  transform-origin: top right;
  transition: transform 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
  z-index: 10;
}
html.has-preview-bar-active .c-preview-bar {
  transform: translate3d(0, 0, 0);
}
html.has-preview-bar-active .c-preview-bar:before {
  transform: scale3d(1, 1, 1);
  transition-delay: 0.1s;
}
@media (hover: none) {
  .c-preview-bar {
    display: none;
  }
}

.c-preview-bar_outer {
  position: relative;
  overflow: hidden;
}

.c-preview-bar_inner {
  display: flex;
  justify-content: space-between;
  font-size: 1.875rem;
  padding-top: 0.875rem;
  padding-bottom: 0.875rem;
  transform: translate3d(0, 100%, 0);
  transition: transform 0.2s cubic-bezier(0.215, 0.61, 0.355, 1);
}
html.has-preview-bar-active .c-preview-bar_inner {
  transform: translate3d(0, 0, 0);
  transition-delay: 0.1s;
}
@media (max-width: 999px) {
  .c-preview-bar_inner {
    font-size: 1.25rem;
  }
}
@media (max-width: 699px) {
  .c-preview-bar_inner {
    font-size: 0.875rem;
  }
}

.c-preview-bar_col {
  width: calc((100vw - 13.125rem) * 10 / 20 + 5.625rem);
  margin-bottom: -0.2em;
  line-height: 0;
}
@media (max-width: 999px) {
  .c-preview-bar_col {
    width: auto;
  }
}
@media (min-width: 1000px) {
  .c-preview-bar_col:nth-child(2) {
    padding-right: calc((100vw - 13.125rem) * 2 / 20 + 1.25rem);
  }
}

.c-preview-bar_tag {
  position: absolute;
  top: 0;
  right: 0.625rem;
  padding-top: 0.875rem;
}
@media (max-width: 999px) {
  .c-preview-bar_tag {
    display: none;
  }
}

.c-preview-bar_col span,
.c-preview-bar_tag span {
  position: relative;
  display: inline-block;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  max-width: 100%;
  line-height: initial;
}
.c-preview-bar_col span:before,
.c-preview-bar_tag span:before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0.2em;
  right: 0;
  z-index: -1;
  background-color: #d3fd50;
  animation: previewBarUpdateField 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
  animation-fill-mode: forwards;
  transform-origin: center;
}

@keyframes previewBarUpdateField {
  0% {
    transform: scale3d(1, 1, 1);
  }
  100% {
    transform: scale3d(1, 0, 1);
  }
}
.c-blog-list_header {
  display: grid;
  grid-template-columns: repeat(20, 1fr);
  padding-top: 10rem;
  padding-bottom: 6.25rem;
}
@media (min-width: 700px) {
  .c-blog-list_header {
    padding-top: 12.5rem;
    padding-bottom: 12.5rem;
  }
}

.c-blog-list_filters {
  display: flex;
  flex-direction: column;
  gap: 0.3125rem;
}
@media (min-width: 700px) {
  .c-blog-list_filters {
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
  }
}

.c-blog-list_filters_container {
  display: flex;
  gap: 1px;
}
@media (max-width: 999px) {
  .c-blog-list_filters_container {
    display: flex;
    flex-wrap: wrap;
  }
}

.c-blog-list_filters_el {
  white-space: nowrap;
  background-color: #ededed;
  padding: 0.3125rem 0.46875rem 0px 0.46875rem;
  transition: color 0.15s cubic-bezier(0.215, 0.61, 0.355, 1),
    background-color 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
  position: relative;
  overflow: hidden;
  display: inline-flex;
}
.c-blog-list_filters_el.-active {
  color: #ffffff;
  background-color: #000000;
}
.c-blog-list_filters_el.-active:hover {
  background-color: #d3fd50;
}
.c-blog-list_filters_el:hover::after {
  height: 100%;
}
.c-blog-list_filters_el:hover .c-blog-list_details_tags_slider_wrapper {
  height: 100%;
}
.c-blog-list_filters_el:hover .c-blog-list_details_tags_slider_container {
  opacity: 1;
}

.c-blog-list_details_tags {
  display: flex;
  flex-wrap: wrap;
  gap: 1px;
}

.c-blog-list_details_tags_slider_wrapper {
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: #d3fd50;
  z-index: 3;
  left: 0;
  top: 0;
  clip-path: inset(0px);
  height: 0px;
  transition: height 0.5s cubic-bezier(0.215, 0.61, 0.355, 1);
}

.c-blog-list_details_tags_slider_container {
  position: absolute;
  transition: opacity 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
  left: 0;
  opacity: 0;
  animation: slide 2s linear infinite;
  padding-top: 0.3125rem;
}

.c-blog-list_details_tags_slider_el {
  position: absolute;
  color: #000000 !important;
}
.c-blog-list_details_tags_slider_el:nth-of-type(1) {
  position: relative;
}
.c-blog-list_details_tags_slider_el:nth-of-type(2) {
  left: calc(1 * 100% + 1 * 5px);
}
.c-blog-list_details_tags_slider_el:nth-of-type(3) {
  left: calc(2 * 100% + 2 * 5px);
}
.c-blog-list_details_tags_slider_el:nth-of-type(4) {
  left: calc(3 * 100% + 3 * 5px);
}
.c-blog-list_details_tags_slider_el:nth-of-type(5) {
  left: calc(4 * 100% + 4 * 5px);
}
.c-blog-list_details_tags_slider_el:nth-of-type(6) {
  left: calc(5 * 100% + 5 * 5px);
}
.c-blog-list_details_tags_slider_el:nth-of-type(7) {
  left: calc(6 * 100% + 6 * 5px);
}
.c-blog-list_details_tags_slider_el:nth-of-type(8) {
  left: calc(7 * 100% + 7 * 5px);
}
.c-blog-list_details_tags_slider_el:nth-of-type(9) {
  left: calc(8 * 100% + 8 * 5px);
}
.c-blog-list_details_tags_slider_el:nth-of-type(10) {
  left: calc(9 * 100% + 9 * 5px);
}
.c-blog-list_details_tags_slider_el:nth-of-type(11) {
  left: calc(10 * 100% + 10 * 5px);
}
.c-blog-list_details_tags_slider_el:nth-of-type(12) {
  left: calc(11 * 100% + 11 * 5px);
}
.c-blog-list_details_tags_slider_el:nth-of-type(13) {
  left: calc(12 * 100% + 12 * 5px);
}
.c-blog-list_details_tags_slider_el:nth-of-type(14) {
  left: calc(13 * 100% + 13 * 5px);
}
.c-blog-list_details_tags_slider_el:nth-of-type(15) {
  left: calc(14 * 100% + 14 * 5px);
}
.c-blog-list_details_tags_slider_el:nth-of-type(16) {
  left: calc(15 * 100% + 15 * 5px);
}
.c-blog-list_details_tags_slider_el:nth-of-type(17) {
  left: calc(16 * 100% + 16 * 5px);
}
.c-blog-list_details_tags_slider_el:nth-of-type(18) {
  left: calc(17 * 100% + 17 * 5px);
}
.c-blog-list_details_tags_slider_el:nth-of-type(19) {
  left: calc(18 * 100% + 18 * 5px);
}
.c-blog-list_details_tags_slider_el:nth-of-type(20) {
  left: calc(19 * 100% + 19 * 5px);
}

@keyframes slide {
  from {
    transform: translateX(0%);
  }
  to {
    transform: translateX(calc(-100% - 5px));
  }
}
.c-blog-list_title {
  display: flex;
  align-items: center;
  gap: 0.625rem;
}
@media (min-width: 700px) {
  .c-blog-list_title {
    grid-column: 3/5;
  }
}

.c-blog-list_title_text {
  line-height: 0.75 !important;
}

.c-blog-list_container {
  min-height: 100vh;
  display: flex;
  flex-wrap: wrap;
  border-top: 1px solid #000000;
  gap: 0.625rem;
  margin-top: 0.625rem;
  padding-top: 0.625rem;
}

.c-blog-list_el_image {
  position: relative;
  border-radius: 50px;
  overflow: hidden;
}
.c-blog-list_el_image:hover .c-blog-list_el_image_inner img {
  transform: scale3d(1.05, 1.05, 1);
}

.c-blog-list_el_image_inner img {
  -o-object-fit: cover;
  object-fit: cover;
  width: 100%;
  height: 100%;
  transform: scale3d(1, 1, 1);
  transition: transform 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.c-blog-list_link:hover .c-blog-list_el_image_inner img {
  transform: scale3d(1.1, 1, 1);
}

.c-blog-list_el {
  position: relative;
  margin-bottom: 3.125rem;
  font-size: 1.375rem;
  display: flex;
  flex-direction: column;
  gap: 0.84375rem;
}
@media (min-width: 700px) {
  .c-blog-list_el {
    flex: 1 1 calc(33.33% - 10px);
    max-width: calc(33.33% - 10px);
    margin-bottom: 5rem;
  }
}
.c-blog-list_el .c-blog-list_el_image_inner {
  aspect-ratio: 466/314;
}
@media (min-width: 700px) {
  .c-blog-list_el:nth-of-type(1),
  .c-blog-list_el:nth-of-type(2) {
    flex: 1 1 calc(50% - 10px);
    max-width: calc(50% - 10px);
  }
}
.c-blog-list_el:nth-of-type(1) .c-blog-list_details_title,
.c-blog-list_el:nth-of-type(2) .c-blog-list_details_title {
  text-wrap: balance;
}
@media (min-width: 700px) {
  .c-blog-list_el:nth-of-type(1) .c-blog-list_details_title,
  .c-blog-list_el:nth-of-type(2) .c-blog-list_details_title {
    font-size: 2.1875rem;
  }
}
.c-blog-list_el:nth-of-type(1) .c-blog-list_details_title:hover,
.c-blog-list_el:nth-of-type(2) .c-blog-list_details_title:hover {
  text-decoration: underline;
}
.c-blog-list_el:nth-of-type(1) .c-blog-list_el_image_inner,
.c-blog-list_el:nth-of-type(2) .c-blog-list_el_image_inner {
  aspect-ratio: 705/476;
}

.c-blog-list_el_inner {
  position: relative;
}
.c-blog-list_el_inner:hover .c-blog-list_details_title {
  text-decoration: underline;
}

.c-blog-list_details_title {
  text-transform: uppercase;
  line-height: 1 !important;
  font-weight: 600;
  max-width: 85%;
  font-size: 1.375rem;
}

.c-blog-list_details {
  display: flex;
  flex-direction: column;
  gap: 0.84375rem;
  margin-top: 1.09375rem;
}

.c-blog-list_details_date {
  display: flex;
  gap: 0.3125rem;
}

.c-blog-list_pagination {
  position: relative;
  display: flex;
  justify-content: space-between;
  margin-top: 2.5rem;
  margin-bottom: 6.25rem;
  align-items: center;
}
@media (min-width: 700px) {
  .c-blog-list_pagination {
    margin-top: 3.75rem;
    margin-bottom: 7.5rem;
  }
}

.c-blog-list_pagination_icon {
  width: 3.125rem;
  aspect-ratio: 77/58;
}
.c-blog-list_pagination_icon:nth-of-type(1) {
  transform: rotate(180deg);
}
.c-blog-list_pagination_icon.-disabled {
  opacity: 0.3;
  pointer-events: none;
}

.c-blog-list_pagination_container {
  display: flex;
  gap: 0.625rem;
}

.c-blog-list_pagination_number {
  font-size: 2.1875rem;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  position: relative;
}
.c-blog-list_pagination_number::after {
  content: "";
  position: absolute;
  height: 2px;
  width: 100%;
  left: 0;
  top: 100%;
  transform: scaleX(0);
  background-color: #000000;
  transition: transform 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
  transform-origin: center;
}
.-active .c-blog-list_pagination_number::after {
  transform: scaleX(1);
}
.c-blog-list_pagination_number:hover::after,
.c-blog-list_pagination_number.-active::after {
  transform: scaleX(1);
}
@media (min-width: 1000px) {
  .c-blog-list_pagination_number {
    font-size: 5rem;
  }
}

.c-blog-details_header {
  display: grid;
  grid-template-columns: repeat(20, 1fr);
  padding-top: 10rem;
  padding-bottom: 3.75rem;
}
@media (min-width: 700px) {
  .c-blog-details_header {
    padding-top: 15rem;
    padding-bottom: 6.25rem;
  }
}

.c-blog-details_title {
  display: flex;
  align-items: center;
  gap: 0.625rem;
}

.c-blog-details_header_inner {
  display: flex;
  flex-direction: column;
  gap: 0.3125rem;
  grid-column: 1/-1;
}
@media (min-width: 700px) {
  .c-blog-details_header_inner {
    grid-column: 3/8;
  }
}

.c-blog-details_intro {
  display: flex;
  flex-direction: column;
  grid-template-columns: repeat(20, 1fr);
}

.c-blog-details_intro_title {
  font-size: 2.25rem;
  text-transform: uppercase;
  line-height: 0.8 !important;
  font-weight: 600;
  text-align: center;
  margin-bottom: 0.3125rem;
}
@media (min-width: 700px) {
  .c-blog-details_intro_title {
    font-size: 5vw;
    margin-left: calc((100vw - 13.125rem) * 1 / 20 + 0.625rem);
    margin-right: calc((100vw - 13.125rem) * 1 / 20 + 0.625rem);
  }
}
@media (min-width: 1400px) {
  .c-blog-details_intro_title {
    font-size: 6.25rem;
  }
}

.c-blog-details_image {
  width: 100%;
}

.c-blog-details_image_inner {
  position: relative;
  border-radius: 50px;
  overflow: hidden;
  width: 100%;
  aspect-ratio: 705/476;
}

.c-blog-details_image img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
}

.c-blog-details_intro_inner {
  display: flex;
  flex-direction: column;
  gap: 2.5rem;
}
@media (min-width: 700px) {
  .c-blog-details_intro_inner {
    gap: 0px;
    flex-direction: row;
    align-items: flex-start;
  }
}

.c-blog-details_intro_author_container {
  order: 1;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  gap: 10px;
}
@media (min-width: 700px) {
  .c-blog-details_intro_author_container {
    padding-top: calc((100vw - 13.125rem) * 2 / 20 + 1.25rem);
    order: unset;
    width: calc((100vw - 13.125rem) * 4 / 20 + 2.5rem);
  }
}
@media (min-width: 1200px) {
  .c-blog-details_intro_author_container {
    margin-right: calc((100vw - 13.125rem) * 1 / 20 + 0.625rem);
    width: calc((100vw - 13.125rem) * 3 / 20 + 1.875rem);
  }
}

.c-blog-details_intro_author {
  display: flex;
  gap: 10px;
  align-items: flex-start;
}

.c-blog-details_intro_author_details {
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-top: 0.125rem;
}
@media (min-width: 1400px) {
  .c-blog-details_intro_author_details {
    flex-shrink: 0;
  }
}

.c-blog-details_intro_author_name {
  text-transform: uppercase;
  line-height: 1.2 !important;
}

.c-blog-details_author_image_inner {
  width: 60px;
  aspect-ratio: 61/64;
  overflow: hidden;
  -o-object-fit: cover;
  object-fit: cover;
  border-radius: 10px;
}
@media (min-width: 700px) {
  .c-blog-details_author_image_inner {
    width: calc((100vw - 13.125rem) * 1 / 20 + 0.625rem);
  }
}

.c-blog-details_author_image img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
}

.c-blog-details_spacing {
  padding-left: 0.3125rem;
  max-width: 70%;
}
@media (min-width: 700px) {
  .c-blog-details_spacing {
    padding-top: calc((100vw - 13.125rem) * 2 / 20 + 1.25rem);
    width: calc((100vw - 13.125rem) * 4 / 20 + 2.5rem);
  }
}
@media (min-width: 1200px) {
  .c-blog-details_spacing {
    width: calc((100vw - 13.125rem) * 3 / 20 + 1.875rem);
    margin-right: calc((100vw - 13.125rem) * 1 / 20 + 0.625rem);
  }
}

.c-blog-details_tags_container {
  display: flex;
  align-items: flex-start;
  gap: 0.3125rem;
  max-width: 70%;
  flex-wrap: wrap;
  flex-shrink: 0;
}

.c-blog-details_excerpt {
  border-top: 1px solid #000000;
  padding-top: 10px;
  margin-top: 5rem;
  font-size: 1.3125rem;
  line-height: 1 !important;
}
@media (min-width: 1000px) {
  .c-blog-details_excerpt {
    font-size: 2.1875rem;
  }
}
@media (min-width: 1200px) {
  .c-blog-details_excerpt {
    margin-top: 7.5rem;
  }
}

.c-blog-details_excerpt_inner {
  text-wrap: balance;
}
@media (min-width: 700px) {
  .c-blog-details_excerpt_inner {
    max-width: calc((100vw - 13.125rem) * 16 / 20 + 10rem);
  }
}
@media (min-width: 1200px) {
  .c-blog-details_excerpt_inner {
    max-width: calc((100vw - 13.125rem) * 10 / 20 + 6.25rem);
  }
}

.c-blog-details_container {
  padding-top: 5rem;
  margin: 0 auto;
  align-items: center;
}
@media (min-width: 1000px) {
  .c-blog-details_container {
    padding-top: 7.5rem;
  }
}

.c-blog-details_author_footer {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
  justify-content: flex-start;
  border-top: 1px solid black;
  padding-top: 10px;
  margin-top: 7.5rem;
  align-items: flex-start;
  margin-bottom: 7.5rem;
}
@media (min-width: 1000px) {
  .c-blog-details_author_footer {
    display: grid;
    grid-template-columns: repeat(20, 1fr);
  }
}

.c-blog-details_author_footer_text {
  line-height: 1.2 !important;
}
@media (min-width: 1000px) {
  .c-blog-details_author_footer_text {
    grid-column: 11/20;
    width: calc((100vw - 13.125rem) * 5 / 20 + 3.125rem);
  }
}

.c-blog-details_author_footer_wrapper {
  display: flex;
  gap: 10px;
  align-items: flex-start;
  align-items: center;
  grid-column: 1/11;
}

.c-blog-details_related {
  padding-top: 10px;
  border-top: 1px solid black;
  margin-top: 7.5rem;
}

.c-blog-details_related_container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.c-blog-details_related_header {
  display: flex;
  align-items: center;
  gap: 0.3125rem;
  margin-bottom: 3.75rem;
}

.c-blog-details_related_header_title {
  text-transform: uppercase;
  font-weight: 600;
  transform: translateY(2px);
}

.c-blog-details_related_header .c-blog-list_filters_el {
  text-transform: uppercase;
  font-weight: 600;
}

.o-article-wysiwyg {
  width: 100%;
  margin: 0 auto;
}
.o-article-wysiwyg > :first-child {
  margin-top: 0 !important;
  padding-top: 0 !important;
}
.o-article-wysiwyg > :last-child {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}
.o-article-wysiwyg p,
.o-article-wysiwyg img,
.o-article-wysiwyg table,
.o-article-wysiwyg blockquote {
  margin-top: 1.25rem;
  margin-bottom: 1.25rem;
}
.o-article-wysiwyg h1,
.o-article-wysiwyg h2,
.o-article-wysiwyg h3,
.o-article-wysiwyg h4,
.o-article-wysiwyg h5,
.o-article-wysiwyg h6 {
  margin-top: 2em;
  margin-block-end: 0.8em;
  text-wrap: balance;
}
@media (min-width: 1000px) {
  .o-article-wysiwyg {
    max-width: calc((100vw - 13.125rem) * 10 / 20 + 6.25rem);
  }
}
.o-article-wysiwyg a {
  text-decoration: underline;
  line-height: 1.3 !important;
}
.o-article-wysiwyg p {
  line-height: 1.3 !important;
}
.o-article-wysiwyg h2 {
  font-size: 1.625rem;
  font-weight: 600;
  line-height: 1.2 !important;
  text-transform: uppercase;
}
@media (min-width: 1000px) {
  .o-article-wysiwyg h2 {
    font-size: 2.1875rem;
  }
}
.o-article-wysiwyg h3 {
  font-size: 1.375rem;
  font-weight: 600;
  line-height: 1.2 !important;
  text-transform: uppercase;
}
.o-article-wysiwyg ul {
  list-style-type: disc;
  padding-left: 1em;
}
.o-article-wysiwyg ol {
  list-style-type: decimal;
}
.o-article-wysiwyg li {
  line-height: 1.1 !important;
  padding-left: 0.5em;
}
.o-article-wysiwyg th {
  font-size: 1.375rem;
  text-align: start;
  vertical-align: top;
  padding: 1.25rem;
}
.o-article-wysiwyg tr:nth-child(odd) {
  background-color: #f2f2f2;
}
.o-article-wysiwyg td {
  width: 50%;
  padding: 1.25rem;
  vertical-align: top;
  text-align: start;
}

.o-article-img {
  margin: 0 auto;
}

.o-article-img_legend {
  padding-top: 0.625rem;
}

.o-article-img {
  width: 100%;
}
.o-article-img .o-article-img_inner {
  aspect-ratio: 706/476;
  border-radius: 50px;
  overflow: hidden;
}
.o-article-img.-full .o-article-img_inner {
  aspect-ratio: 1440/830;
  border-radius: 0px;
}
@media (min-width: 1000px) {
  .o-article-img.-small,
  .o-article-img.c-footer_legals_link {
    width: calc((100vw - 13.125rem) * 10 / 20 + 6.25rem);
  }
  .o-article-img.-large,
  .c-project-summary_text p.o-article-img,
  .o-article-img.c-block-text {
    width: calc((100vw - 13.125rem) * 14 / 20 + 8.75rem);
  }
  .o-article-img.-full {
    width: calc(100% + 1.25rem);
    margin-left: -0.625rem;
    margin-right: -0.625rem;
    margin-top: 2.5rem;
    margin-bottom: 2.5rem;
  }
}

.o-article-img_inner img {
  width: 100%;
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
}

.c-accordion_summary {
  cursor: pointer;
  list-style: none;
  font-size: 1.375rem;
  line-height: 1.2 !important;
  display: flex;
  padding-top: 10px;
  justify-content: space-between;
}
.c-accordion_summary::marker,
.c-accordion_summary::-webkit-details-marker {
  display: none;
}

.c-accordion_label {
  max-width: 80%;
  text-wrap: balance;
}

.c-accordion_content {
  padding-top: 1.875rem;
}

.c-accordion_add_icon {
  position: relative;
  width: 40px;
  height: 40px;
  flex-shrink: 0;
  transition: transform 0.3s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.c-accordion.is-active .c-accordion_add_icon {
  transform: rotate(90deg);
}

.c-accordion_add_icon div {
  width: 100%;
  height: 1px;
  background-color: #000000;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translateX(-50%) translateY(-50%);
}
.c-accordion_add_icon div:nth-of-type(2) {
  transform: rotate(90deg);
  left: 0%;
}

.c-faq {
  display: flex;
  flex-direction: column;
  gap: 1.875rem;
  width: 100%;
  margin: 0 auto;
}
@media (min-width: 1000px) {
  .c-faq {
    max-width: calc((100vw - 13.125rem) * 10 / 20 + 6.25rem);
  }
}

.c-faq_title {
  font-size: 1.625rem;
  text-transform: uppercase;
  font-weight: 600;
  line-height: 1.2;
  text-wrap: balance;
}
@media (min-width: 1000px) {
  .c-faq_title {
    font-size: 2.25rem;
  }
}

.c-blockquote {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
  margin: 0 auto;
}
@media (min-width: 1000px) {
  .c-blockquote {
    width: calc((100vw - 13.125rem) * 15 / 20 + 9.375rem);
  }
}

.c-blockquote_content {
  font-size: 2.25rem;
  line-height: 1 !important;
  margin: 0px;
  margin: 0 auto;
}
@media (min-width: 1000px) {
  .c-blockquote_content {
    font-size: 3.5rem;
  }
}

.c-blockquote_icon {
  display: inline-block;
  width: 35px;
  margin-right: 5.625rem;
  transform: translateY(-10px);
}
.c-blockquote_icon.-rotate {
  transform: rotate(180deg);
}

.c-blockquote_icon svg {
  height: 35px;
}

.c-blockquote-author {
  display: flex;
  gap: 0.9375rem;
}

/* stylelint-disable */
.u-2\:1::before {
  padding-bottom: 50%;
}

.u-4\:3::before {
  padding-bottom: 75%;
}

.u-16\:9::before {
  padding-bottom: 56.25%;
}

/* stylelint-enable */
.u-1\/1 {
  width: 100% !important;
}

.u-1\/2 {
  width: 50% !important;
}

.u-2\/2 {
  width: 100% !important;
}

.u-1\/3 {
  width: 33.3333333333% !important;
}

.u-2\/3 {
  width: 66.6666666667% !important;
}

.u-3\/3 {
  width: 100% !important;
}

.u-1\/4 {
  width: 25% !important;
}

.u-2\/4 {
  width: 50% !important;
}

.u-3\/4 {
  width: 75% !important;
}

.u-4\/4 {
  width: 100% !important;
}

.u-1\/5 {
  width: 20% !important;
}

.u-2\/5 {
  width: 40% !important;
}

.u-3\/5 {
  width: 60% !important;
}

.u-4\/5 {
  width: 80% !important;
}

.u-5\/5 {
  width: 100% !important;
}

.u-1\/6 {
  width: 16.6666666667% !important;
}

.u-2\/6 {
  width: 33.3333333333% !important;
}

.u-3\/6 {
  width: 50% !important;
}

.u-4\/6 {
  width: 66.6666666667% !important;
}

.u-5\/6 {
  width: 83.3333333333% !important;
}

.u-6\/6 {
  width: 100% !important;
}

.u-1\/7 {
  width: 14.2857142857% !important;
}

.u-2\/7 {
  width: 28.5714285714% !important;
}

.u-3\/7 {
  width: 42.8571428571% !important;
}

.u-4\/7 {
  width: 57.1428571429% !important;
}

.u-5\/7 {
  width: 71.4285714286% !important;
}

.u-6\/7 {
  width: 85.7142857143% !important;
}

.u-7\/7 {
  width: 100% !important;
}

.u-1\/8 {
  width: 12.5% !important;
}

.u-2\/8 {
  width: 25% !important;
}

.u-3\/8 {
  width: 37.5% !important;
}

.u-4\/8 {
  width: 50% !important;
}

.u-5\/8 {
  width: 62.5% !important;
}

.u-6\/8 {
  width: 75% !important;
}

.u-7\/8 {
  width: 87.5% !important;
}

.u-8\/8 {
  width: 100% !important;
}

@media (min-width: 700px) {
  .u-1\/2\@from-small {
    width: 50%;
  }
}

@media (min-width: 700px) {
  .u-1\/3\@from-small {
    width: 33.3333333333%;
  }
}

@media (min-width: 700px) {
  .u-2\/3\@from-small {
    width: 66.6666666667%;
  }
}

@media (min-width: 1000px) {
  .u-2\/5\@from-medium {
    width: 40%;
  }
}

@media (min-width: 1000px) {
  .u-3\/5\@from-medium {
    width: 60%;
  }
}

.u-clearfix::after {
  display: block;
  clear: both;
  content: "";
}

.u-z-index-0 {
  z-index: 0;
}

.u-z-index-1 {
  z-index: 1;
}

.u-z-index-2 {
  z-index: 2;
}

.u-z-index-3 {
  z-index: 3;
}

.u-z-index-4 {
  z-index: 4;
}

.u-z-index-5 {
  z-index: 5;
}

.u-z-index-6 {
  z-index: 6;
}

.u-z-index-7 {
  z-index: 7;
}

.u-z-index-8 {
  z-index: 8;
}

.u-z-index-9 {
  z-index: 9;
}

.u-z-index-10 {
  z-index: 10;
}

.u-inline {
  display: inline;
}

.u-block {
  display: block;
}

.u-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  word-wrap: normal;
  max-width: 100%;
}

.u-indent {
  text-indent: 5em;
}

.u-font-size-inherit {
  font-size: inherit;
}

[hidden][aria-hidden="false"] {
  position: absolute;
  display: inherit;
  clip: rect(0, 0, 0, 0);
}

[hidden][aria-hidden="false"]:focus {
  clip: auto;
}

/**
 * Completely remove from the flow but leave available to screen readers.
 */
.u-screen-reader-text {
  position: absolute !important;
  overflow: hidden;
  clip: rect(0 0 0 0);
  margin: 0;
  padding: 0;
  width: 1px;
  height: 1px;
  border: 0;
}

@media not print {
  .u-screen-reader-text\@screen {
    position: absolute !important;
    overflow: hidden;
    clip: rect(0 0 0 0);
    margin: 0;
    padding: 0;
    width: 1px;
    height: 1px;
    border: 0;
  }
}
/*
 * Extends the `.screen-reader-text` class to allow the element
 * to be focusable when navigated to via the keyboard.
 *
 * @link https://www.drupal.org/node/897638
 * @todo Define styles when focused.
 */
.u-screen-reader-text.-focusable:focus,
.u-screen-reader-text.-focusable:active {
  clip: auto;
  width: auto;
  height: auto;
}

/* stylelint-disable string-quotes */
.u-padding {
  padding: 2.5rem !important;
}

.u-padding-small {
  padding: 0.625rem !important;
}

.u-padding-double {
  padding: 5rem !important;
}

.u-padding-none {
  padding: 0rem !important;
}

.u-padding-top {
  padding-top: 2.5rem !important;
}

.u-padding-top-small {
  padding-top: 0.625rem !important;
}

.u-padding-top-double {
  padding-top: 5rem !important;
}

.u-padding-top-none {
  padding-top: 0rem !important;
}

.u-padding-right {
  padding-right: 2.5rem !important;
}

.u-padding-right-small {
  padding-right: 0.625rem !important;
}

.u-padding-right-double {
  padding-right: 5rem !important;
}

.u-padding-right-none {
  padding-right: 0rem !important;
}

.u-padding-bottom {
  padding-bottom: 2.5rem !important;
}

.u-padding-bottom-small {
  padding-bottom: 0.625rem !important;
}

.u-padding-bottom-double {
  padding-bottom: 5rem !important;
}

.u-padding-bottom-none {
  padding-bottom: 0rem !important;
}

.u-padding-left {
  padding-left: 2.5rem !important;
}

.u-padding-left-small {
  padding-left: 0.625rem !important;
}

.u-padding-left-double {
  padding-left: 5rem !important;
}

.u-padding-left-none {
  padding-left: 0rem !important;
}

.u-padding-horizontal {
  padding-left: 2.5rem !important;
  padding-right: 2.5rem !important;
}

.u-padding-horizontal-small {
  padding-left: 0.625rem !important;
  padding-right: 0.625rem !important;
}

.u-padding-horizontal-double {
  padding-left: 5rem !important;
  padding-right: 5rem !important;
}

.u-padding-horizontal-none {
  padding-left: 0rem !important;
  padding-right: 0rem !important;
}

.u-padding-vertical {
  padding-top: 2.5rem !important;
  padding-bottom: 2.5rem !important;
}

.u-padding-vertical-small {
  padding-top: 0.625rem !important;
  padding-bottom: 0.625rem !important;
}

.u-padding-vertical-double {
  padding-top: 5rem !important;
  padding-bottom: 5rem !important;
}

.u-padding-vertical-none {
  padding-top: 0rem !important;
  padding-bottom: 0rem !important;
}

.u-margin {
  margin: 2.5rem !important;
}

.u-margin-small {
  margin: 0.625rem !important;
}

.u-margin-double {
  margin: 5rem !important;
}

.u-margin-none {
  margin: 0rem !important;
}

.u-margin-top {
  margin-top: 2.5rem !important;
}

.u-margin-top-small {
  margin-top: 0.625rem !important;
}

.u-margin-top-double {
  margin-top: 5rem !important;
}

.u-margin-top-none {
  margin-top: 0rem !important;
}

.u-margin-right {
  margin-right: 2.5rem !important;
}

.u-margin-right-small {
  margin-right: 0.625rem !important;
}

.u-margin-right-double {
  margin-right: 5rem !important;
}

.u-margin-right-none {
  margin-right: 0rem !important;
}

.u-margin-bottom {
  margin-bottom: 2.5rem !important;
}

.u-margin-bottom-small {
  margin-bottom: 0.625rem !important;
}

.u-margin-bottom-double {
  margin-bottom: 5rem !important;
}

.u-margin-bottom-none {
  margin-bottom: 0rem !important;
}

.u-margin-left {
  margin-left: 2.5rem !important;
}

.u-margin-left-small {
  margin-left: 0.625rem !important;
}

.u-margin-left-double {
  margin-left: 5rem !important;
}

.u-margin-left-none {
  margin-left: 0rem !important;
}

.u-margin-horizontal {
  margin-left: 2.5rem !important;
  margin-right: 2.5rem !important;
}

.u-margin-horizontal-small {
  margin-left: 0.625rem !important;
  margin-right: 0.625rem !important;
}

.u-margin-horizontal-double {
  margin-left: 5rem !important;
  margin-right: 5rem !important;
}

.u-margin-horizontal-none {
  margin-left: 0rem !important;
  margin-right: 0rem !important;
}

.u-margin-vertical {
  margin-top: 2.5rem !important;
  margin-bottom: 2.5rem !important;
}

.u-margin-vertical-small {
  margin-top: 0.625rem !important;
  margin-bottom: 0.625rem !important;
}

.u-margin-vertical-double {
  margin-top: 5rem !important;
  margin-bottom: 5rem !important;
}

.u-margin-vertical-none {
  margin-top: 0rem !important;
  margin-bottom: 0rem !important;
}

/* stylelint-enable string-quotes */
.u-anim {
  opacity: 0;
  transform: translate3d(0, -40px, 0);
}
.is-ready .u-anim {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 0s;
}
.is-loading .u-anim {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.u-anim.-delay-1 {
  opacity: 0;
  transform: translate3d(0, -40px, 0);
}
.is-ready .u-anim.-delay-1 {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 0.1s;
}
.is-loading .u-anim.-delay-1 {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.u-anim.-delay-2 {
  opacity: 0;
  transform: translate3d(0, -40px, 0);
}
.is-ready .u-anim.-delay-2 {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 0.2s;
}
.is-loading .u-anim.-delay-2 {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.u-anim.-delay-3 {
  opacity: 0;
  transform: translate3d(0, -40px, 0);
}
.is-ready .u-anim.-delay-3 {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 0.3s;
}
.is-loading .u-anim.-delay-3 {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.u-anim.-delay-4 {
  opacity: 0;
  transform: translate3d(0, -40px, 0);
}
.is-ready .u-anim.-delay-4 {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 0.4s;
}
.is-loading .u-anim.-delay-4 {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.u-anim.-delay-5 {
  opacity: 0;
  transform: translate3d(0, -40px, 0);
}
.is-ready .u-anim.-delay-5 {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 0.5s;
}
.is-loading .u-anim.-delay-5 {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.u-anim.-delay-6 {
  opacity: 0;
  transform: translate3d(0, -40px, 0);
}
.is-ready .u-anim.-delay-6 {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 0.6s;
}
.is-loading .u-anim.-delay-6 {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.u-anim.-delay-7 {
  opacity: 0;
  transform: translate3d(0, -40px, 0);
}
.is-ready .u-anim.-delay-7 {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 0.7s;
}
.is-loading .u-anim.-delay-7 {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.u-anim.-delay-8 {
  opacity: 0;
  transform: translate3d(0, -40px, 0);
}
.is-ready .u-anim.-delay-8 {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 0.8s;
}
.is-loading .u-anim.-delay-8 {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.u-anim.-delay-9 {
  opacity: 0;
  transform: translate3d(0, -40px, 0);
}
.is-ready .u-anim.-delay-9 {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 0.9s;
}
.is-loading .u-anim.-delay-9 {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.u-anim.-delay-10 {
  opacity: 0;
  transform: translate3d(0, -40px, 0);
}
.is-ready .u-anim.-delay-10 {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 1s;
}
.is-loading .u-anim.-delay-10 {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.u-anim.-delay-11 {
  opacity: 0;
  transform: translate3d(0, -40px, 0);
}
.is-ready .u-anim.-delay-11 {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 1.1s;
}
.is-loading .u-anim.-delay-11 {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.u-anim.-delay-12 {
  opacity: 0;
  transform: translate3d(0, -40px, 0);
}
.is-ready .u-anim.-delay-12 {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 1.2s;
}
.is-loading .u-anim.-delay-12 {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.u-anim.-delay-13 {
  opacity: 0;
  transform: translate3d(0, -40px, 0);
}
.is-ready .u-anim.-delay-13 {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 1.3s;
}
.is-loading .u-anim.-delay-13 {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.u-anim.-delay-14 {
  opacity: 0;
  transform: translate3d(0, -40px, 0);
}
.is-ready .u-anim.-delay-14 {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 1.4s;
}
.is-loading .u-anim.-delay-14 {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.u-anim.-delay-15 {
  opacity: 0;
  transform: translate3d(0, -40px, 0);
}
.is-ready .u-anim.-delay-15 {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 1.5s;
}
.is-loading .u-anim.-delay-15 {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.u-anim.-delay-16 {
  opacity: 0;
  transform: translate3d(0, -40px, 0);
}
.is-ready .u-anim.-delay-16 {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 1.6s;
}
.is-loading .u-anim.-delay-16 {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.u-anim.-delay-17 {
  opacity: 0;
  transform: translate3d(0, -40px, 0);
}
.is-ready .u-anim.-delay-17 {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 1.7s;
}
.is-loading .u-anim.-delay-17 {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.u-anim.-delay-18 {
  opacity: 0;
  transform: translate3d(0, -40px, 0);
}
.is-ready .u-anim.-delay-18 {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 1.8s;
}
.is-loading .u-anim.-delay-18 {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.u-anim.-delay-19 {
  opacity: 0;
  transform: translate3d(0, -40px, 0);
}
.is-ready .u-anim.-delay-19 {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 1.9s;
}
.is-loading .u-anim.-delay-19 {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.u-anim.-delay-20 {
  opacity: 0;
  transform: translate3d(0, -40px, 0);
}
.is-ready .u-anim.-delay-20 {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 2s;
}
.is-loading .u-anim.-delay-20 {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}

.u-anim-scroll {
  opacity: 0;
  transform: translate3d(0, -40px, 0);
}
.u-anim-scroll.-reverse {
  transform: translate3d(0, 40px, 0);
}
.is-ready .u-anim-scroll-parent.is-inview .u-anim-scroll.-parent {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 0s;
}
.is-loading .u-anim-scroll.-parent {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.is-ready .u-anim-scroll:not(.-parent).is-inview {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 0s;
}
.is-loading .u-anim-scroll:not(.-parent) {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.u-anim-scroll.-delay-1 {
  opacity: 0;
  transform: translate3d(0, -40px, 0);
}
.u-anim-scroll.-delay-1.-reverse {
  transform: translate3d(0, 40px, 0);
}
.is-ready .u-anim-scroll-parent.is-inview .u-anim-scroll.-delay-1.-parent {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 0.1s;
}
.is-loading .u-anim-scroll.-delay-1.-parent {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.is-ready .u-anim-scroll.-delay-1:not(.-parent).is-inview {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 0.1s;
}
.is-loading .u-anim-scroll.-delay-1:not(.-parent) {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.u-anim-scroll.-delay-2 {
  opacity: 0;
  transform: translate3d(0, -40px, 0);
}
.u-anim-scroll.-delay-2.-reverse {
  transform: translate3d(0, 40px, 0);
}
.is-ready .u-anim-scroll-parent.is-inview .u-anim-scroll.-delay-2.-parent {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 0.2s;
}
.is-loading .u-anim-scroll.-delay-2.-parent {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.is-ready .u-anim-scroll.-delay-2:not(.-parent).is-inview {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 0.2s;
}
.is-loading .u-anim-scroll.-delay-2:not(.-parent) {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.u-anim-scroll.-delay-3 {
  opacity: 0;
  transform: translate3d(0, -40px, 0);
}
.u-anim-scroll.-delay-3.-reverse {
  transform: translate3d(0, 40px, 0);
}
.is-ready .u-anim-scroll-parent.is-inview .u-anim-scroll.-delay-3.-parent {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 0.3s;
}
.is-loading .u-anim-scroll.-delay-3.-parent {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.is-ready .u-anim-scroll.-delay-3:not(.-parent).is-inview {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 0.3s;
}
.is-loading .u-anim-scroll.-delay-3:not(.-parent) {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.u-anim-scroll.-delay-4 {
  opacity: 0;
  transform: translate3d(0, -40px, 0);
}
.u-anim-scroll.-delay-4.-reverse {
  transform: translate3d(0, 40px, 0);
}
.is-ready .u-anim-scroll-parent.is-inview .u-anim-scroll.-delay-4.-parent {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 0.4s;
}
.is-loading .u-anim-scroll.-delay-4.-parent {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.is-ready .u-anim-scroll.-delay-4:not(.-parent).is-inview {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 0.4s;
}
.is-loading .u-anim-scroll.-delay-4:not(.-parent) {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.u-anim-scroll.-delay-5 {
  opacity: 0;
  transform: translate3d(0, -40px, 0);
}
.u-anim-scroll.-delay-5.-reverse {
  transform: translate3d(0, 40px, 0);
}
.is-ready .u-anim-scroll-parent.is-inview .u-anim-scroll.-delay-5.-parent {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 0.5s;
}
.is-loading .u-anim-scroll.-delay-5.-parent {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.is-ready .u-anim-scroll.-delay-5:not(.-parent).is-inview {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 0.5s;
}
.is-loading .u-anim-scroll.-delay-5:not(.-parent) {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.u-anim-scroll.-delay-6 {
  opacity: 0;
  transform: translate3d(0, -40px, 0);
}
.u-anim-scroll.-delay-6.-reverse {
  transform: translate3d(0, 40px, 0);
}
.is-ready .u-anim-scroll-parent.is-inview .u-anim-scroll.-delay-6.-parent {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 0.6s;
}
.is-loading .u-anim-scroll.-delay-6.-parent {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.is-ready .u-anim-scroll.-delay-6:not(.-parent).is-inview {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 0.6s;
}
.is-loading .u-anim-scroll.-delay-6:not(.-parent) {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.u-anim-scroll.-delay-7 {
  opacity: 0;
  transform: translate3d(0, -40px, 0);
}
.u-anim-scroll.-delay-7.-reverse {
  transform: translate3d(0, 40px, 0);
}
.is-ready .u-anim-scroll-parent.is-inview .u-anim-scroll.-delay-7.-parent {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 0.7s;
}
.is-loading .u-anim-scroll.-delay-7.-parent {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.is-ready .u-anim-scroll.-delay-7:not(.-parent).is-inview {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 0.7s;
}
.is-loading .u-anim-scroll.-delay-7:not(.-parent) {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.u-anim-scroll.-delay-8 {
  opacity: 0;
  transform: translate3d(0, -40px, 0);
}
.u-anim-scroll.-delay-8.-reverse {
  transform: translate3d(0, 40px, 0);
}
.is-ready .u-anim-scroll-parent.is-inview .u-anim-scroll.-delay-8.-parent {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 0.8s;
}
.is-loading .u-anim-scroll.-delay-8.-parent {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.is-ready .u-anim-scroll.-delay-8:not(.-parent).is-inview {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 0.8s;
}
.is-loading .u-anim-scroll.-delay-8:not(.-parent) {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.u-anim-scroll.-delay-9 {
  opacity: 0;
  transform: translate3d(0, -40px, 0);
}
.u-anim-scroll.-delay-9.-reverse {
  transform: translate3d(0, 40px, 0);
}
.is-ready .u-anim-scroll-parent.is-inview .u-anim-scroll.-delay-9.-parent {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 0.9s;
}
.is-loading .u-anim-scroll.-delay-9.-parent {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.is-ready .u-anim-scroll.-delay-9:not(.-parent).is-inview {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 0.9s;
}
.is-loading .u-anim-scroll.-delay-9:not(.-parent) {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.u-anim-scroll.-delay-10 {
  opacity: 0;
  transform: translate3d(0, -40px, 0);
}
.u-anim-scroll.-delay-10.-reverse {
  transform: translate3d(0, 40px, 0);
}
.is-ready .u-anim-scroll-parent.is-inview .u-anim-scroll.-delay-10.-parent {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 1s;
}
.is-loading .u-anim-scroll.-delay-10.-parent {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.is-ready .u-anim-scroll.-delay-10:not(.-parent).is-inview {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 1s;
}
.is-loading .u-anim-scroll.-delay-10:not(.-parent) {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.u-anim-scroll.-delay-11 {
  opacity: 0;
  transform: translate3d(0, -40px, 0);
}
.u-anim-scroll.-delay-11.-reverse {
  transform: translate3d(0, 40px, 0);
}
.is-ready .u-anim-scroll-parent.is-inview .u-anim-scroll.-delay-11.-parent {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 1.1s;
}
.is-loading .u-anim-scroll.-delay-11.-parent {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.is-ready .u-anim-scroll.-delay-11:not(.-parent).is-inview {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 1.1s;
}
.is-loading .u-anim-scroll.-delay-11:not(.-parent) {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.u-anim-scroll.-delay-12 {
  opacity: 0;
  transform: translate3d(0, -40px, 0);
}
.u-anim-scroll.-delay-12.-reverse {
  transform: translate3d(0, 40px, 0);
}
.is-ready .u-anim-scroll-parent.is-inview .u-anim-scroll.-delay-12.-parent {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 1.2s;
}
.is-loading .u-anim-scroll.-delay-12.-parent {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.is-ready .u-anim-scroll.-delay-12:not(.-parent).is-inview {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 1.2s;
}
.is-loading .u-anim-scroll.-delay-12:not(.-parent) {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.u-anim-scroll.-delay-13 {
  opacity: 0;
  transform: translate3d(0, -40px, 0);
}
.u-anim-scroll.-delay-13.-reverse {
  transform: translate3d(0, 40px, 0);
}
.is-ready .u-anim-scroll-parent.is-inview .u-anim-scroll.-delay-13.-parent {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 1.3s;
}
.is-loading .u-anim-scroll.-delay-13.-parent {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.is-ready .u-anim-scroll.-delay-13:not(.-parent).is-inview {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 1.3s;
}
.is-loading .u-anim-scroll.-delay-13:not(.-parent) {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.u-anim-scroll.-delay-14 {
  opacity: 0;
  transform: translate3d(0, -40px, 0);
}
.u-anim-scroll.-delay-14.-reverse {
  transform: translate3d(0, 40px, 0);
}
.is-ready .u-anim-scroll-parent.is-inview .u-anim-scroll.-delay-14.-parent {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 1.4s;
}
.is-loading .u-anim-scroll.-delay-14.-parent {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.is-ready .u-anim-scroll.-delay-14:not(.-parent).is-inview {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 1.4s;
}
.is-loading .u-anim-scroll.-delay-14:not(.-parent) {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.u-anim-scroll.-delay-15 {
  opacity: 0;
  transform: translate3d(0, -40px, 0);
}
.u-anim-scroll.-delay-15.-reverse {
  transform: translate3d(0, 40px, 0);
}
.is-ready .u-anim-scroll-parent.is-inview .u-anim-scroll.-delay-15.-parent {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 1.5s;
}
.is-loading .u-anim-scroll.-delay-15.-parent {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.is-ready .u-anim-scroll.-delay-15:not(.-parent).is-inview {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 1.5s;
}
.is-loading .u-anim-scroll.-delay-15:not(.-parent) {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.u-anim-scroll.-delay-16 {
  opacity: 0;
  transform: translate3d(0, -40px, 0);
}
.u-anim-scroll.-delay-16.-reverse {
  transform: translate3d(0, 40px, 0);
}
.is-ready .u-anim-scroll-parent.is-inview .u-anim-scroll.-delay-16.-parent {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 1.6s;
}
.is-loading .u-anim-scroll.-delay-16.-parent {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.is-ready .u-anim-scroll.-delay-16:not(.-parent).is-inview {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 1.6s;
}
.is-loading .u-anim-scroll.-delay-16:not(.-parent) {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.u-anim-scroll.-delay-17 {
  opacity: 0;
  transform: translate3d(0, -40px, 0);
}
.u-anim-scroll.-delay-17.-reverse {
  transform: translate3d(0, 40px, 0);
}
.is-ready .u-anim-scroll-parent.is-inview .u-anim-scroll.-delay-17.-parent {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 1.7s;
}
.is-loading .u-anim-scroll.-delay-17.-parent {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.is-ready .u-anim-scroll.-delay-17:not(.-parent).is-inview {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 1.7s;
}
.is-loading .u-anim-scroll.-delay-17:not(.-parent) {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.u-anim-scroll.-delay-18 {
  opacity: 0;
  transform: translate3d(0, -40px, 0);
}
.u-anim-scroll.-delay-18.-reverse {
  transform: translate3d(0, 40px, 0);
}
.is-ready .u-anim-scroll-parent.is-inview .u-anim-scroll.-delay-18.-parent {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 1.8s;
}
.is-loading .u-anim-scroll.-delay-18.-parent {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.is-ready .u-anim-scroll.-delay-18:not(.-parent).is-inview {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 1.8s;
}
.is-loading .u-anim-scroll.-delay-18:not(.-parent) {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.u-anim-scroll.-delay-19 {
  opacity: 0;
  transform: translate3d(0, -40px, 0);
}
.u-anim-scroll.-delay-19.-reverse {
  transform: translate3d(0, 40px, 0);
}
.is-ready .u-anim-scroll-parent.is-inview .u-anim-scroll.-delay-19.-parent {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 1.9s;
}
.is-loading .u-anim-scroll.-delay-19.-parent {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.is-ready .u-anim-scroll.-delay-19:not(.-parent).is-inview {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 1.9s;
}
.is-loading .u-anim-scroll.-delay-19:not(.-parent) {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.u-anim-scroll.-delay-20 {
  opacity: 0;
  transform: translate3d(0, -40px, 0);
}
.u-anim-scroll.-delay-20.-reverse {
  transform: translate3d(0, 40px, 0);
}
.is-ready .u-anim-scroll-parent.is-inview .u-anim-scroll.-delay-20.-parent {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 2s;
}
.is-loading .u-anim-scroll.-delay-20.-parent {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
.is-ready .u-anim-scroll.-delay-20:not(.-parent).is-inview {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
  transition-delay: 2s;
}
.is-loading .u-anim-scroll.-delay-20:not(.-parent) {
  transition-duration: 0.3s;
  transition: opacity 0.6s cubic-bezier(0.215, 0.61, 0.355, 1),
    transform 0.6s cubic-bezier(0.215, 0.61, 0.355, 1);
}
